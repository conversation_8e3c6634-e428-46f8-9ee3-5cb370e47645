package com.jinghang.cash.utils;

import cn.hutool.core.util.ObjectUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Scanner;

public class DailyDataAllocator {
    
    public static void main(String[] args) {
       // getDailyDataAllocator("1000",3);
    }

    public static Map<Integer,Double> getDailyDataAllocator(String amount,String month,int count){
        System.out.println("当月每日数据分配计算");
        int daysInMonth;
        if(count > 0){
            daysInMonth = count;
        }else{
            if(ObjectUtil.isEmpty(month)){
                if(!month.contains("-")){
                    throw new RuntimeException("所属月份格式错误");
                }
                String[] split = month.split("-");
                LocalDate localDate = LocalDate.of(Integer.parseInt(split[0]),Integer.parseInt(split[1]),0);
                YearMonth currentYearMonth = YearMonth.from(localDate);
                daysInMonth = currentYearMonth.lengthOfMonth();
                System.out.println("当前月份: " + currentYearMonth.getMonth() + " " + currentYearMonth.getYear());
                System.out.println("当月天数: " + daysInMonth);
            }else{
                // 获取当前年月
                LocalDate today = LocalDate.now();
                YearMonth currentYearMonth = YearMonth.from(today);
                daysInMonth = currentYearMonth.lengthOfMonth();
                System.out.println("当前月份: " + currentYearMonth.getMonth() + " " + currentYearMonth.getYear());
                System.out.println("当月天数: " + daysInMonth);
            }
        }
        // 获取总数
        double total = Double.parseDouble(amount);
        // 计算每日平均值
        double dailyAverage = total / daysInMonth;
        System.out.printf("每日平均值: %.10f%n", dailyAverage);
        // 检查是否能除尽
        if (isDivisible(total, daysInMonth)) {
            System.out.println("总数可以整除天数，每天数据相同。");
            double[] dailyData = new double[daysInMonth];
            Arrays.fill(dailyData, roundToTwoDecimalPlaces(dailyAverage));
            // 显示结果
            return displayResults(dailyData, total);
        } else {
            System.out.println("总数无法整除天数，将进行调整。");
            // 计算调整后的每日数据
            double[] dailyData = calculateAdjustedData(total, daysInMonth);
            // 验证总和
            double sum = Arrays.stream(dailyData).sum();
            System.out.printf("验证总和: %.2f (原始总数: %.2f)%n", sum, total);
            // 显示结果
            return displayResults(dailyData, total);

        }
    }

    // 计算调整后的每日数据
    private static double[] calculateAdjustedData(double total, int daysInMonth) {
        double[] dailyData = new double[daysInMonth];
        // 计算平均值（保留更多小数位用于计算）
        BigDecimal bdTotal = new BigDecimal(Double.toString(total));
        BigDecimal bdDays = new BigDecimal(daysInMonth);
        BigDecimal dailyAvg = bdTotal.divide(bdDays, 10, RoundingMode.HALF_UP);
        // 前n-1天使用四舍五入到两位小数的平均值
        double roundedAvg = roundToTwoDecimalPlaces(dailyAvg.doubleValue());
        for (int i = 0; i < daysInMonth - 1; i++) {
            dailyData[i] = roundedAvg;
        }
        // 计算前n-1天的总和
        BigDecimal sumFirstDays = new BigDecimal(roundedAvg * (daysInMonth - 1));
        // 最后一天 = 总数 - 前n-1天的总和
        BigDecimal lastDay = bdTotal.subtract(sumFirstDays);
        dailyData[daysInMonth - 1] = lastDay.setScale(2, RoundingMode.HALF_UP).doubleValue();
        System.out.printf("前 %d 天的数据: %.2f%n", daysInMonth - 1, roundedAvg);
        System.out.printf("前 %d 天的总和: %.2f%n", daysInMonth - 1, sumFirstDays.doubleValue());
        System.out.printf("最后一天的数据: %.2f%n", dailyData[daysInMonth - 1]);
        return dailyData;
    }
    
    // 检查是否能除尽100
    private static boolean isDivisible(double dividend, double divisor) {
        BigDecimal bd1 = new BigDecimal(Double.toString(dividend));
        BigDecimal bd2 = new BigDecimal(Double.toString(divisor));
        try {
            bd1.divide(bd2);
            return true;
        } catch (ArithmeticException e) {
            return false;
        }
    }
    
    // 四舍五入到两位小数
    private static double roundToTwoDecimalPlaces(double value) {
        BigDecimal bd = new BigDecimal(Double.toString(value));
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        return bd.doubleValue();
    }
    
    // 显示结果
    private static Map<Integer,Double> displayResults(double[] dailyData, double total) {
        System.out.println("\n每日数据分配结果:");
        Map<Integer,Double> map = new HashMap<>(dailyData.length);
        double sum = 0;
        for (int i = 0; i < dailyData.length; i++) {
            System.out.printf("第 %2d 天: %.2f%n", i + 1, dailyData[i]);
            sum += dailyData[i];
            map.put(i + 1,dailyData[i]);
        }
        System.out.printf("总和: %.2f (原始总数: %.2f)%n", sum, total);
        return map;
    }
}