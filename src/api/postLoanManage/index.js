import request from '@/utils/request'

// 减免订单列表查询
export function offlineRepayReduceSearchPage(data) {
  return request({
    url: '/afterLoan/queryRepayReducePage',
    method: 'post',
    data
  })
}

// 销账记录列表查询
export function offlineRepayApplySearchPage(data) {
  return request({
    url: '/afterLoan/queryRepayApplyPage',
    method: 'post',
    data
  })
}

// 减免订单-审核
export function audit(data) {
  return request({
    url: '/afterLoan/audit',
    method: 'post',
    data
  })
}

// 查询还款计划
export function queryRepayPlan(data) {
  return request({
    url: '/afterLoan/queryRepayPlan',
    method: 'post',
    data
  })
}

// 试算
export function trial(data) {
  return request({
    url: '/afterLoan/trial',
    method: 'post',
    data
  })
}

// 减免申请
export function reduceApply(data) {
  return request({
    url: '/afterLoan/reduceApply',
    method: 'post',
    data
  })
}


// 销账预览
export function repayApplyPreview(data) {
  return request({
    url: '/afterLoan/repayApplyPreview',
    method: 'post',
    data
  })
}

// 销账
export function repayApply(data) {
  return request({
    url: '/afterLoan/repayApply',
    method: 'post',
    data
  })
}

// 聚合支付提交订单
export function aggregatePayApply(data) {
  return request({
    url: '/afterLoan/aggregatePayApply',
    method: 'post',
    data
  })
}

// 聚合支付记录
export function queryAggregatePayPage(data) {
  return request({
    url: '/afterLoan/queryAggregatePayPage',
    method: 'post',
    data
  })
}

// 发送支付链接短信
export function sendPayUrlSms(data) {
  return request({
    url: '/afterLoan/sendPayUrlSms',
    method: 'post',
    data
  })
}

// 聚合支付记录导出
export function downloadExcel(data) {
  return request({
    url: '/afterLoan/downloadExcel',
    method: 'post',
    data,
    responseType: "arraybuffer",
  })
}

// 查询聚合支付配置所有信息
export function aggregatePayConfig() {
  return request({
    url: '/scanCode/aggregatePayConfig',
    method: 'post',
  })
}


// 修改聚合支付配置信息
export function updateConfig(data) {
  return request({
    url: '/scanCode/updateConfig',
    method: 'post',
    data
  })
}

