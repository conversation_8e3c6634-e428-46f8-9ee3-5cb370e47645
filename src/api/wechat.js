import request from '@/utils/request'

// 企业微信授权登录
export function wechatLogin(data) {
  return request({
    url: 'auth/wechat/login',
    method: 'post',
    data
  })
}

// 企业微信授权码登录
export function wechatCodeLogin(data) {
  return request({
    url: 'auth/wechat/code-login',
    method: 'post',
    data
  })
}

// 获取企业微信配置
export function getWechatConfig() {
  return request({
    url: 'auth/wechat/config',
    method: 'get'
  })
}

// 绑定企业微信账号
export function bindWechatAccount(data) {
  return request({
    url: 'auth/wechat/bind',
    method: 'post',
    data
  })
}

// 解绑企业微信账号
export function unbindWechatAccount() {
  return request({
    url: 'auth/wechat/unbind',
    method: 'post'
  })
}
