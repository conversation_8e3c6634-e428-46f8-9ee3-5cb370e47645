// 移动端样式
@import 'variables';

// 移动端基础样式
.mobile-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
  font-size: 14px;
  line-height: 1.5;
}

// 移动端响应式断点
$mobile-breakpoint: 768px;

// 移动端专用媒体查询
@mixin mobile-only {
  @media (max-width: $mobile-breakpoint) {
    @content;
  }
}

// 移动端卡片样式
.mobile-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
  overflow: hidden;

  .card-header {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    font-weight: 500;
    color: #333;
  }

  .card-body {
    padding: 15px;
  }

  .card-footer {
    padding: 15px;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
  }
}

// 移动端按钮样式
.mobile-btn {
  width: 100%;
  height: 44px;
  border-radius: 22px;
  font-size: 16px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s;

  &.primary {
    background: $--color-primary;
    color: #fff;

    &:hover {
      background: darken($--color-primary, 10%);
    }

    &:active {
      background: darken($--color-primary, 15%);
    }
  }

  &.secondary {
    background: #f5f5f5;
    color: #666;

    &:hover {
      background: #e5e5e5;
    }

    &:active {
      background: #d5d5d5;
    }
  }

  &.danger {
    background: $--color-danger;
    color: #fff;

    &:hover {
      background: darken($--color-danger, 10%);
    }

    &:active {
      background: darken($--color-danger, 15%);
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// 移动端表单样式
.mobile-form {
  .form-item {
    margin-bottom: 20px;

    .form-label {
      display: block;
      margin-bottom: 8px;
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }

    .form-input {
      width: 100%;
      height: 44px;
      padding: 0 15px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 16px;
      background: #fff;
      transition: border-color 0.3s;

      &:focus {
        outline: none;
        border-color: $--color-primary;
      }

      &::placeholder {
        color: #999;
      }
    }

    .form-textarea {
      width: 100%;
      min-height: 80px;
      padding: 12px 15px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 16px;
      background: #fff;
      resize: vertical;
      transition: border-color 0.3s;

      &:focus {
        outline: none;
        border-color: $--color-primary;
      }

      &::placeholder {
        color: #999;
      }
    }

    .form-error {
      margin-top: 5px;
      font-size: 12px;
      color: $--color-danger;
    }
  }
}

// 移动端列表样式
.mobile-list {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;

  .list-item {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.2s;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: #f5f5f5;
    }

    .item-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #f0f7ff;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;

      i {
        font-size: 18px;
        color: $--color-primary;
      }
    }

    .item-content {
      flex: 1;

      .item-title {
        font-size: 16px;
        color: #333;
        margin-bottom: 4px;
      }

      .item-desc {
        font-size: 14px;
        color: #666;
      }
    }

    .item-extra {
      color: #999;
      font-size: 14px;
    }

    .item-arrow {
      margin-left: 10px;
      color: #ccc;
      font-size: 14px;
    }
  }
}

// 移动端标签页样式
.mobile-tabs {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;

  .tabs-header {
    display: flex;
    border-bottom: 1px solid #f0f0f0;

    .tab-item {
      flex: 1;
      padding: 15px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s;
      position: relative;

      &.active {
        color: $--color-primary;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 30px;
          height: 2px;
          background: $--color-primary;
        }
      }
    }
  }

  .tabs-content {
    padding: 15px;
  }
}

// 移动端加载样式
.mobile-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #999;

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f0f0f0;
    border-top: 3px solid $--color-primary;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
  }

  .loading-text {
    font-size: 14px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 移动端空状态样式
.mobile-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #999;

  .empty-icon {
    font-size: 64px;
    margin-bottom: 15px;
    opacity: 0.5;
  }

  .empty-text {
    font-size: 16px;
    margin-bottom: 10px;
  }

  .empty-desc {
    font-size: 14px;
    text-align: center;
    line-height: 1.5;
  }
}

// 移动端安全区域适配
.mobile-safe-area {
  padding-bottom: env(safe-area-inset-bottom);
}

// 移动端滚动优化
.mobile-scroll {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}

// 移动端触摸反馈
.mobile-touch {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

// 移动端字体大小适配
@include mobile-only {
  html {
    font-size: 14px;
  }

  .el-button--large {
    padding: 12px 20px;
    font-size: 16px;
  }

  .el-button--small {
    padding: 8px 15px;
    font-size: 12px;
  }

  .el-input__inner {
    height: 44px;
    line-height: 44px;
    font-size: 16px;
  }

  .el-form-item__label {
    font-size: 14px;
  }

  .el-table {
    font-size: 12px;
  }

  .el-dialog {
    width: 90% !important;
    margin: 5vh auto !important;
  }
}
