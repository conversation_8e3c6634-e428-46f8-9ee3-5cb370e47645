package com.jinghang.ppd.api.dto.repay;



import com.jinghang.ppd.api.enums.RepayPurpose;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class RepayApplyDto {

    /**
     * 还款方式
     */
    private RepayPurpose repayPurpose;

    /**
     * 期数
     */
    private Integer period;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 实还金额
     */
    private BigDecimal amount;

    /**
     * 溢出金额
     */
    private BigDecimal overflowAmount;

    private String outerRepayNo;

    private String repayDate;

    public BigDecimal getOverflowAmount() {
        return overflowAmount;
    }

    public void setOverflowAmount(BigDecimal overflowAmount) {
        this.overflowAmount = overflowAmount;
    }

    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getOuterRepayNo() {
        return outerRepayNo;
    }

    public void setOuterRepayNo(String outerRepayNo) {
        this.outerRepayNo = outerRepayNo;
    }

    public String getRepayDate() {
        return repayDate;
    }

    public void setRepayDate(String repayDate) {
        this.repayDate = repayDate;
    }
}
