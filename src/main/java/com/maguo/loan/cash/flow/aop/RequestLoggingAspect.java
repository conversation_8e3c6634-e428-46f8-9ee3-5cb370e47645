package com.maguo.loan.cash.flow.aop;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maguo.loan.cash.flow.annotation.LogRequest;
import com.maguo.loan.cash.flow.entity.CreditApplication;
import com.maguo.loan.cash.flow.entity.common.ProjectProductMapping;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.repository.CreditApplicationRepository;
import com.maguo.loan.cash.flow.repository.ProjectProductMappingRepository;
import com.maguo.loan.cash.flow.util.AgeUtil;
import com.maguo.loan.cash.flow.util.GioPushUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 授信申请日志切面。
 * 拦截 @LogRequest 注解，在方法执行前后进行数据库操作
 */
@Aspect
@Component
public class RequestLoggingAspect {
    private static final Logger logger = LoggerFactory.getLogger(RequestLoggingAspect.class);
    private final CreditApplicationRepository creditApplicationRepository;
    private final ObjectMapper objectMapper;
    private static final ThreadLocal<String> APPLICATION_LOG_ID = new ThreadLocal<>();
    private static final int MAX_BYTE_VALUE = 255;
    private final ProjectProductMappingRepository projectProductMappingRepository;


    public RequestLoggingAspect(CreditApplicationRepository creditApplicationRepository, ObjectMapper objectMapper,
                                ProjectProductMappingRepository projectProductMappingRepository) {
        this.creditApplicationRepository = creditApplicationRepository;
        this.objectMapper = objectMapper;
        this.projectProductMappingRepository = projectProductMappingRepository;
    }

    /**
     * 定义切点，匹配所有被 @LogRequest 注解的方法。
     */
    @Pointcut("within(com.maguo.loan.cash.flow.entrance..*)")
    public void inControllerPackage() {
    }


    /**
     * 方法执行前：解析请求，将数据存入数据库，状态为“处理中”
     */
    @Before("inControllerPackage() && @annotation(logRequest)")
    public void beforeAdvice(JoinPoint joinPoint, LogRequest logRequest) {
        try {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
            String requestBody = (String) request.getAttribute("decryptedRequestBody");
            if (requestBody == null || requestBody.isEmpty()) {
                return;
            }
            CreditApplication application = buildApplicationFromRequest(requestBody, logRequest.flowIdentifier());
            application.setCreditStatus(ProcessState.PROCESSING);
            CreditApplication saved = creditApplicationRepository.save(application);
            APPLICATION_LOG_ID.set(saved.getId());
            logger.info("请求已入库, ID: {}", saved.getId());
        } catch (Exception e) {
            logger.error("请求入库（@Before）失败", e);
            APPLICATION_LOG_ID.remove();
        }
    }

    /**
     * 方法成功返回后：更新状态为“成功”
     */
    @AfterReturning("inControllerPackage() && @annotation(com.maguo.loan.cash.flow.annotation.LogRequest)")
    public void afterReturningAdvice() {
        updateStatus(ProcessState.SUCCESS, null);
    }

    /**
     * 方法抛出异常后：更新状态为“失败”，并记录原因
     */
    @AfterThrowing(pointcut = "inControllerPackage() && @annotation(com.maguo.loan.cash.flow.annotation.LogRequest)", throwing = "ex")
    public void afterThrowingAdvice(Throwable ex) {
        updateStatus(ProcessState.FAIL, ex.getMessage());
    }

    /**
     * 统一的状态更新方法
     */
    private void updateStatus(ProcessState status, String reason) {
        String id = APPLICATION_LOG_ID.get();
        if (id == null) {
            return;
        }
        try {
            creditApplicationRepository.findById(id).ifPresent(app -> {
                app.setCreditStatus(status);
                if (reason != null) {
                    // 截取异常信息，防止超长
                    app.setFailReason(reason.substring(0, Math.min(reason.length(), MAX_BYTE_VALUE)));
                }
                creditApplicationRepository.save(app);
                logger.info("更新请求记录状态为: {}, ID: {}", status, id);
            });
        } catch (Exception e) {
            logger.error("更新请求记录状态失败, ID: {}", id, e);
        } finally {
            APPLICATION_LOG_ID.remove(); // 完成操作后，务必清理ThreadLocal
        }
    }

    /**
     * 从请求JSON中提取数据并构建实体。
     *
     * */
    private CreditApplication buildApplicationFromRequest(String requestBody, String flowIdentifier) {
        try {
            CreditApplication app = new CreditApplication();
            JsonNode rootNode = objectMapper.readTree(requestBody);
            app.setApplyTime(LocalDateTime.now());
            if (FlowChannel.LVXIN.name().equals(flowIdentifier)) {
                app.setExternalOrderNo(rootNode.path("partnerUserId").asText());
                app.setCustomerName(rootNode.path("name").asText("未知"));
                app.setIdType("ID_CARD");
                app.setProductCode(rootNode.path("productType").asText());
                app.setIdNo(rootNode.path("idCard").asText());
                app.setMobile(rootNode.path("phone").asText());
                app.setCreditAmount(new BigDecimal(rootNode.path("creditAmount").asText("0")));
                app.setApplyPeriod(rootNode.path("applyPeriod").asInt(0));
                Integer calculatedAge = AgeUtil.calculateAge(app.getIdNo());
                app.setAge(calculatedAge);
                String gender = GioPushUtil.getGenderFromIDCard(app.getIdNo());
                app.setGender(gender);
                app.setFlowChannel(FlowChannel.LVXIN.name());
                String bankChannel = rootNode.path("productType").asText();
                String channelName;
                switch (bankChannel) {
                    case "01":
                    case "02":
                        channelName = "CYBK";
                        break;
                    case "03":
                    case "04":
                        channelName = "HXBK";
                        break;
                    default:
                        channelName = "未知渠道";
                        break;
                }
                Optional<ProjectProductMapping> productMapping = projectProductMappingRepository.findByProductCode(bankChannel);
                if (productMapping.isPresent()) {
                    app.setProjectCode(productMapping.get().getProjectCode());
                } else {
                    app.setProjectCode("");
                }
                app.setBankChannel(channelName);
                logger.info("bankChannel={}, channelName={}", bankChannel, channelName);
            } else if (FlowChannel.PPCJDL.name().equals(flowIdentifier)) {

                app.setCustomerName(rootNode.path("custName").asText("未知"));
                app.setIdType("ID_CARD");
                app.setExternalOrderNo(rootNode.path("loanReqNo").asText());
                app.setIdNo(rootNode.path("idNo").asText());
                app.setMobile(rootNode.path("mobileNo").asText());
                String sourceCode = rootNode.path("sourceCode").asText();
                String accessType = rootNode.path("accessType").asText();
                String productCodes = sourceCode + "-" + accessType;
                app.setProductCode(productCodes);
                app.setCreditAmount(new BigDecimal(rootNode.path("loanAmt").asText("0")));
                app.setApplyPeriod(rootNode.path("loanTerm").asInt(0));
                Integer calculateAge = AgeUtil.calculateAge(app.getIdNo());
                app.setAge(calculateAge);
                String gender = GioPushUtil.getGenderFromIDCard(app.getIdNo());
                app.setGender(gender);
                app.setFlowChannel(FlowChannel.PPCJDL.name());
                String channelName;

                if (sourceCode.contains("HB")) {
                    channelName = "HXBK";
                } else if (sourceCode.contains("CY")) {
                    channelName = "CYBK";
                } else {
                    channelName = "未知渠道";
                }
                String productMapping = sourceCode + "_" + accessType;
                Optional<ProjectProductMapping> product = projectProductMappingRepository.findByProductCode(productMapping);
                if (product.isPresent()) {
                    app.setProjectCode(product.get().getProjectCode());
                } else {
                    app.setProjectCode("");
                }
                app.setBankChannel(channelName);
                logger.info("sourceCode={}, channelName={}", sourceCode, channelName);
            } else if (FlowChannel.LTFQ.name().equals(flowIdentifier)) {
                app.setExternalOrderNo(rootNode.path("outApplSeq").asText());
                app.setCustomerName(rootNode.path("name").asText("未知"));
                app.setIdType("ID_CARD");
                app.setProductCode(rootNode.path("channel").asText());
                app.setIdNo(rootNode.path("idCard").asText());
                app.setMobile(rootNode.path("phone").asText());
                app.setCreditAmount(new BigDecimal(rootNode.path("creditAmount").asText("0")));
                app.setApplyPeriod(rootNode.path("applyPeriod").asInt(0));
                Integer calculatedAge = AgeUtil.calculateAge(app.getIdNo());
                app.setAge(calculatedAge);
                String gender = GioPushUtil.getGenderFromIDCard(app.getIdNo());
                app.setGender(gender);
                app.setFlowChannel(FlowChannel.LTFQ.name());
                String bankChannel = rootNode.path("channel").asText();
                String channelName = "CYBK";
                Optional<ProjectProductMapping> productMapping = projectProductMappingRepository.findByProductCode(bankChannel);
                if (productMapping.isPresent()) {
                    app.setProjectCode(productMapping.get().getProjectCode());
                } else {
                    app.setProjectCode("");
                }
                app.setBankChannel(channelName);
                logger.info("bankChannel={}, channelName={}", bankChannel, channelName);
            }
            else {
                logger.warn("未知的流量标识: {}, 无法解析业务数据。", flowIdentifier);
                return null;
            }

            return app;
        } catch (IOException e) {
            logger.error("解析请求JSON失败, flowIdentifier: {}, body: {}", flowIdentifier, requestBody, e);
            return null;
        }
    }
}
