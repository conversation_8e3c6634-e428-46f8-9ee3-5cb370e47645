package com.maguo.loan.cash.flow.entrance.bairong.dto.settlefile;


import com.maguo.loan.cash.flow.entrance.bairong.enums.BairongSettleFileStatus;

public class SettleFileApplyResult {
    /**
     * 结果描述
     * 11001-开具处理中
     * 11002-开具成功
     * 11003-开具失败
     */
    private String status;
    /**
     * 失败原因
     */
    private String remark;


    public static SettleFileApplyResult fail(String errMsg) {
        SettleFileApplyResult result = new SettleFileApplyResult();
        result.setStatus(BairongSettleFileStatus.FAIL.getStatus());
        result.setRemark(errMsg);
        return result;
    }

    public static SettleFileApplyResult fail() {
        SettleFileApplyResult result = new SettleFileApplyResult();
        result.setStatus(BairongSettleFileStatus.FAIL.getStatus());
        result.setRemark(BairongSettleFileStatus.FAIL.getDesc());
        return result;
    }

    public static SettleFileApplyResult build(String status, String desc) {
        SettleFileApplyResult result = new SettleFileApplyResult();
        result.setStatus(status);
        result.setRemark(desc);
        return result;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

}
