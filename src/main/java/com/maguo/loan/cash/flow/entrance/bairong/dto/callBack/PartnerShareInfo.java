package com.maguo.loan.cash.flow.entrance.bairong.dto.callBack;

import lombok.Data;

/**
 * 权益订单通知 - 机构多商户分账信息子实体类
 * 备注：用于描述单个商户的分账及分账退款详情
 */
@Data
public class PartnerShareInfo {

    /**
     * 机构分账商户号
     * 是否必填：N（否）
     * 备注：由“权益方+融担方”标识的商户号
     */
    private String partnerShareMerchantNo;

    /**
     * 机构分账金额
     * 是否必填：N（否）
     * 备注：该商户可获得的分账金额（单位：元）
     */
    private Double partnerShareAmount;

    /**
     * 机构分账退款金额
     * 是否必填：N（否）
     * 备注：该商户分账金额对应的退款金额（单位：元）
     */
    private Double partnerShareRefundAmount;
}
