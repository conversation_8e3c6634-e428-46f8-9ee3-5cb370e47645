package com.maguo.loan.cash.flow.entrance.bairong.dto.callBack;

import lombok.Data;

import java.util.List;

@Data
public class BairongBenefitOrderNotifyRequest {

    /**
     * 资金方借据号
     * 是否必填：Y（是）
     * 唯一标识资金方的借据
     */
    private String loanNo;

    /**
     * 渠道放款流水号
     * 是否必填：Y（是）
     * 唯一标识渠道的放款流水
     */
    private String outLoanSeq;

    /**
     * 权益方订单号
     * 是否必填：Y（是）
     * 备注：唯一标识权益方生成的订单
     */
    private String benefitOrderNo;

    /**
     * 订单金额
     * 是否必填：Y（是）
     * 备注：权益费金额（单位：元）
     */
    private Double orderAmount;

    /**
     * 订单状态
     * 是否必填：Y（是）
     * 备注：
     * 1-待支付【不会出现该状态】
     * 2-支付成功
     * 3-支付失败
     * 4-退款中【不会出现该状态】
     * 5-退款成功
     * 6-退款失败
     * 7-订单取消
     */
    private Integer status;

    /**
     * 订单创建时间
     * 是否必填：Y（是）
     * 备注：毫秒级时间戳（如：1716888888888）
     */
    private Long createTime;

    /**
     * 支付时间
     * 是否必填：N（否）
     * 备注：毫秒级时间戳，仅“支付成功”状态时传递
     */
    private Long payTime;

    /**
     * 支付流水号
     * 是否必填：N（否）
     * 备注：仅“支付成功”状态时传递，唯一标识支付记录
     */
    private String paymentNo;

    /**
     * 支付通道
     * 是否必填：Y（是）
     * 备注：可选值：
     * BAOFU-宝付支付
     * ALLINPAY-通联支付
     * YEEPAY-易宝支付
     * JDPAY-京东支付
     * DXMPAY-度小满支付
     * KQIANPAY-快钱支付
     */
    private String paymentChannel;

    /**
     * 订单过期时间
     * 是否必填：N（否）
     * 备注：毫秒级时间戳，标识订单失效时间
     */
    private Long expireTime;

    /**
     * 退款金额
     * 是否必填：N（否）
     * 备注：多笔退款时，此值为退款总金额（单位：元）
     */
    private Double refundAmount;

    /**
     * 机构分账金额
     * 是否必填：N（否）
     * 备注：机构可获得的分账金额（单位：元）
     */
    private Double splitAccountAmount;

    /**
     * 机构多商户分账信息集合
     * 是否必填：N（否）
     * 备注：包含多个商户的分账详情，类型为 List<PartnerShareInfo>
     */
    private List<PartnerShareInfo> partnerShareList;

    /**
     * 机构分账退款金额
     * 是否必填：N（否）
     * 备注：机构分账金额对应的退款总金额（单位：元）
     */
    private Double shareRefundAmount;

    /**
     * 多次退款信息集合
     * 是否必填：N（否）
     * 备注：包含订单的多笔退款详情，类型为 List<RefundInfo>
     */
    private List<RefundInfo> refundInfoList;

    /**
     * 支付失败原因
     * 是否必填：N（否）
     * 备注：仅“支付失败”状态时传递，记录最新一次扣款失败的原因
     */
    private String failReason;

    /**
     * 权益服务商
     * 是否必填：Y（是）
     * 备注：可选值：
     * 1-钛翮科技
     * 2-众奚科技
     * 3-及未科技
     */
    private String benefitServiceProvider;
}
