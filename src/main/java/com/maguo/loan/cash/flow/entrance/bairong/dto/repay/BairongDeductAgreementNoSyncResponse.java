package com.maguo.loan.cash.flow.entrance.bairong.dto.repay;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @ClassName BaiRongDeductAgreementNoSyncResponse
 * <AUTHOR>
 * @Description 扣款协议号同步响应参数
 * @Date 2025/9/11 16:50
 * @Version v1.0
 **/
@Data
public class BairongDeductAgreementNoSyncResponse {
    /**
     * 渠道申请流水号
     * 必填（与同步请求时的渠道申请流水号一致，用于关联请求）
     */
    @NotBlank(message = "渠道申请流水号不能为空")
    private String outSignSeq;
    /**
     * 资金方申请流水号
     * 非必填（仅同步成功时返回，用于资金方侧追踪）
     */
    private String signSeq;
    /**
     * 同步状态
     * 非必填（1：成功，2：失败）
     */
    private String status;
    /**
     * 状态描述
     * 非必填（同步失败时返回具体失败原因，成功时可空）
     */
    private String statusDesc;

    /**
     * 判断扣款协议号同步是否成功
     *
     * @return true - 同步成功（状态为 1），false - 同步失败或状态未返回
     */
    public boolean isSyncSuccess() {
        return "1".equals(status);
    }

    /**
     * 判断扣款协议号同步是否失败
     *
     * @return true - 同步失败（状态为 2），false - 同步成功或状态未返回
     */
    public boolean isSyncFailed() {
        return "2".equals(status);
    }

    /**
     * 校验状态描述的合法性：同步失败时必须返回状态描述
     *
     * @return true - 校验通过，false - 失败时缺少状态描述
     */
    public boolean isStatusDescValid() {
        // 若同步失败，状态描述不能为空；成功时无要求
        if (isSyncFailed()) {
            return statusDesc != null && !statusDesc.trim().isEmpty();
        }
        return true;
    }

    /**
     * 校验资金方流水号的合法性：同步成功时必须返回资金方申请流水号
     *
     * @return true - 校验通过，false - 成功时缺少资金方流水号
     */
    public boolean isSignSeqValid() {
        // 若同步成功，资金方申请流水号不能为空；失败时无要求
        if (isSyncSuccess()) {
            return signSeq != null && !signSeq.trim().isEmpty();
        }
        return true;
    }

    //todo 在接收响应后，可通过以下逻辑快速处理同步结果
    /*
    // 2. 判断同步结果并处理
    if (responseDTO.isSyncSuccess()) {
        // 校验成功场景的必填字段
        if (!responseDTO.isSignSeqValid()) {
            throw new BizException("同步成功但未返回资金方申请流水号");
        }
        // 处理成功逻辑：如更新本地协议同步状态为“成功”，记录资金方流水号
    } else if (responseDTO.isSyncFailed()) {
        // 校验失败场景的必填字段
        if (!responseDTO.isStatusDescValid()) {
            throw new BizException("同步失败但未返回失败原因");
        }
        // 处理失败逻辑：如记录失败原因，触发重试机制
    } else {
        // 处理状态未返回的异常场景
        throw new BizException("扣款协议号同步状态未知，无法处理");
    }*/


}
