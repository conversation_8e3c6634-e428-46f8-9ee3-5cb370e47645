package com.maguo.loan.cash.flow.job.bairong;

import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.recc.ReccApplyDto;
import com.jinghang.capital.api.dto.recc.ReccResultDto;
import com.jinghang.capital.api.dto.recc.ReccType;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.entity.bairong.vo.BairongLoanDetailVo;
import com.maguo.loan.cash.flow.entrance.bairong.config.BairongConfig;
import com.maguo.loan.cash.flow.entrance.bairong.service.BairongReconService;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.remote.core.FinReccService;
import com.maguo.loan.cash.flow.service.JHReconService;
import com.maguo.loan.cash.flow.util.SftpUtils;
import com.xxl.job.core.handler.annotation.JobHandler;
import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 百融放款文件txt上传
 * @date 2025/9/12 11:21
 */
@Component
@JobHandler("BairongLoanDetailJob")
public class BairongLoanDetailJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(BairongLoanDetailJob.class);
    private static final String FILE_PREFIX = "loan_";
    private static final String FILE_SUFFIX = ".txt";
    private static final String OK_SUFFIX = ".ok";
    private static final String SLASH = "/";
    @Autowired
    private BairongReconService bairongReconService;
    @Autowired
    private SftpUtils sftpUtils;
    @Autowired
    private FinReccService finReccService;
    @Autowired
    private BairongConfig bairongConfig;
    @Autowired
    private JHReconService jhReconService;

    /**
     * 百融放款文件txt上传
     *
     * @param jobParam
     */
    @Override
    public void doJob(JobParam jobParam) {
        logger.info("生成百融放款文件txt文件开始");
        if (jobParam.getBankChannel() == null) {
            logger.error("资方通道为空，请在定时任务中填写资方通道");
            throw new NullPointerException("bankChannel is null");
        }
        logger.info("当前JobParam参数: {}", JsonUtil.toJsonString(jobParam));
        try {
            // 日期处理
            LocalDate localDate = jobParam.getStartDate() != null
                ? jobParam.getStartDate()
                : LocalDate.now().minusDays(1);
            //保存数据
            jobParam.setTaskHandler("bairongLoanJob");
            jobParam.setTaskDescription("放款文件-百融");
            jobParam.setStartDate(localDate);
            String yesterday = localDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String fileName = FILE_PREFIX + yesterday + FILE_SUFFIX;
            String okFileName = FILE_PREFIX + yesterday + OK_SUFFIX;
            String remoteDir = null;
            remoteDir = bairongConfig.getSftpReconPath() + yesterday + SLASH;
            logger.info("远程目录路径: {}", remoteDir);
            FlowChannel flowChannel = FlowChannel.getFlowChannel(jobParam.getChannel());
            ReccApplyDto reccApplyDto = new ReccApplyDto();
            reccApplyDto.setChannel(jobParam.getBankChannel());
            reccApplyDto.setReccDay(localDate);
            reccApplyDto.setReccType(ReccType.LOAN);
            logger.info("百融放款文件txt上传,对资对账文件接口参数:{}", JsonUtil.toJsonString(reccApplyDto));
            RestResult<ReccResultDto> query = finReccService.query(reccApplyDto);
            logger.info("百融放款文件txt上传,对资对账文件接口返回信息:{}", JsonUtil.toJsonString(query));
            if (!(query.isSuccess() && query.getData().getStatus() == ProcessStatus.SUCCESS)) {
                logger.info("百融放款文件txt上传失败 对资对账文件接口调用失败:{}", query.getData().getFailMsg());
                throw new BizException("跑批结束,对资对账文件异常", ResultCode.BIZ_ERROR);
            }
            logger.info("获取百融放款文件数据 时间: {}, 渠道: {}", localDate, flowChannel);
            // 获取数据
            List<BairongLoanDetailVo> loanInfoVos = bairongReconService.getLoanDetailReconFile(localDate, flowChannel, jobParam.getBankChannel());
            logger.info("获取百融放款文件数据成功，数据量：{}", loanInfoVos.size());
            // 生成文件流
            ByteArrayOutputStream stream = generateCsvToStream(loanInfoVos);
            // sftp上传
            sftpUtils.uploadStreamToPPDSftp(stream, fileName, remoteDir);
            logger.info("百融放款文件上传成功，文件名：{}", fileName);
            // 上传.ok文件
            sftpUtils.uploadStreamToPPDSftp(new ByteArrayOutputStream(), okFileName, remoteDir);
            logger.info("百融放款文件上传成功，文件名：{}", fileName);
            //回调
            jhReconService.saveTaskMonitoringData(jobParam, true, null);
        } catch (Exception e) {
            logger.error("百融放款文件txt上传失败", e);
            jhReconService.saveTaskMonitoringData(jobParam, false, e);
        }
    }

    public ByteArrayOutputStream generateCsvToStream(List<BairongLoanDetailVo> data) throws IOException {

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        OutputStreamWriter writer = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);
        BufferedWriter bw = new BufferedWriter(writer);

        // 写数据（使用|作为分隔符）
        for (BairongLoanDetailVo loan : data) {
            String formattedAmount = String.format("%.2f", Double.parseDouble(BairongReconService.safe(loan.getDnAmt())));
            bw.write(String.join("|", Arrays.asList(
                BairongReconService.safe(loan.getLoanNo()),
                BairongReconService.safe(loan.getLoanActvDt()),
                BairongReconService.safe(loan.getAcctBank()),
                formattedAmount)));
            bw.newLine();
        }

        bw.flush();
        return outputStream;
    }

}
