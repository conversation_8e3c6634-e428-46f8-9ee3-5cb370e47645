package com.maguo.loan.cash.flow.entity;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

@Entity
@Table(name = "bairong_apply_basic_info")
public class BaiRongApplyBasicInfo extends BaseEntity {



    @Column(name = "out_loan_seq", length = 32, nullable = false)
    private String outLoanSeq; // 渠道放款流水号

    @Column(name = "appt_typ", length = 20, nullable = false)
    private String apptTyp; // 申请人类型

    @Column(name = "cust_name", length = 60, nullable = false)
    private String custName; // 申请人名称

    @Column(name = "id_typ", length = 20, nullable = false)
    private String idTyp; // 证件类型

    @Column(name = "id_typ_oth", length = 60)
    private String idTypOth; // 证件类型其他备注

    @Column(name = "id_no", length = 25, nullable = false)
    private String idNo; // 证件号

    @Column(name = "id_no_start_date", length = 10, nullable = false)
    private String idNoStartDate; // 证件有效期开始

    @Column(name = "id_no_end_date", length = 10, nullable = false)
    private String idNoEndDate; // 证件有效期截止

    @Column(name = "id_organ", length = 64, nullable = false)
    private String idOrgan; // 发证机构

    @Column(name = "born_date", length = 10, nullable = false)
    private String bornDate; // 出生日期

    @Column(name = "indiv_mobile", length = 20, nullable = false)
    private String indivMobile; // 手机号

    @Column(name = "indiv_sex", length = 20, nullable = false)
    private String indivSex; // 性别

    @Column(name = "appt_age", nullable = false)
    private Integer apptAge; // 年龄

    @Column(name = "indiv_marital", length = 20, nullable = false)
    private String indivMarital; // 婚姻状况

    @Column(name = "indiv_edu", length = 20, nullable = false)
    private String indivEdu; // 最高学历

    @Column(name = "indiv_degree", length = 20)
    private String indivDegree; // 最高学位

    @Column(name = "id_card_address", length = 200, nullable = false)
    private String idCardAddress; // 户籍地址

    @Column(length = 20, nullable = false)
    private String nation; // 民族

    @Column(name = "account_info_id")
    private String accountInfoId;


    public String getAccountInfoId() {
        return accountInfoId;
    }

    public void setAccountInfoId(String accountInfoId) {
        this.accountInfoId = accountInfoId;
    }

    public String getOutLoanSeq() {
        return outLoanSeq;
    }

    public void setOutLoanSeq(String outLoanSeq) {
        this.outLoanSeq = outLoanSeq;
    }

    public String getApptTyp() {
        return apptTyp;
    }

    public void setApptTyp(String apptTyp) {
        this.apptTyp = apptTyp;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getIdTyp() {
        return idTyp;
    }

    public void setIdTyp(String idTyp) {
        this.idTyp = idTyp;
    }

    public String getIdTypOth() {
        return idTypOth;
    }

    public void setIdTypOth(String idTypOth) {
        this.idTypOth = idTypOth;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getIdNoStartDate() {
        return idNoStartDate;
    }

    public void setIdNoStartDate(String idNoStartDate) {
        this.idNoStartDate = idNoStartDate;
    }

    public String getIdNoEndDate() {
        return idNoEndDate;
    }

    public void setIdNoEndDate(String idNoEndDate) {
        this.idNoEndDate = idNoEndDate;
    }

    public String getIdOrgan() {
        return idOrgan;
    }

    public void setIdOrgan(String idOrgan) {
        this.idOrgan = idOrgan;
    }

    public String getBornDate() {
        return bornDate;
    }

    public void setBornDate(String bornDate) {
        this.bornDate = bornDate;
    }

    public String getIndivMobile() {
        return indivMobile;
    }

    public void setIndivMobile(String indivMobile) {
        this.indivMobile = indivMobile;
    }

    public String getIndivSex() {
        return indivSex;
    }

    public void setIndivSex(String indivSex) {
        this.indivSex = indivSex;
    }

    public Integer getApptAge() {
        return apptAge;
    }

    public void setApptAge(Integer apptAge) {
        this.apptAge = apptAge;
    }

    public String getIndivMarital() {
        return indivMarital;
    }

    public void setIndivMarital(String indivMarital) {
        this.indivMarital = indivMarital;
    }

    public String getIndivEdu() {
        return indivEdu;
    }

    public void setIndivEdu(String indivEdu) {
        this.indivEdu = indivEdu;
    }

    public String getIndivDegree() {
        return indivDegree;
    }

    public void setIndivDegree(String indivDegree) {
        this.indivDegree = indivDegree;
    }

    public String getIdCardAddress() {
        return idCardAddress;
    }

    public void setIdCardAddress(String idCardAddress) {
        this.idCardAddress = idCardAddress;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }
}
