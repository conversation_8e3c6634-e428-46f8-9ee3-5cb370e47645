package com.maguo.loan.cash.flow.entrance.bairong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.jinghang.common.http.exception.HttpException;
import com.jinghang.common.util.HttpUtil;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entity.BairongRepayCallbackRequest;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.RepaymentDetailDto;
import com.maguo.loan.cash.flow.entrance.bairong.config.BairongConfig;
import com.maguo.loan.cash.flow.entrance.bairong.convert.BairongConvert;
import com.maguo.loan.cash.flow.entrance.bairong.dto.callBack.BairongLoanResultCallbackNoticeRequest;
import com.maguo.loan.cash.flow.entrance.bairong.enums.BairongCallbackPathEnum;
import com.maguo.loan.cash.flow.entrance.bairong.enums.BairongLoanStatus;
import com.maguo.loan.cash.flow.entrance.bairong.enums.BairongRepayStatus;
import com.maguo.loan.cash.flow.entrance.bairong.exception.BairongResultCode;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanFailFollowRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.util.DateTimeUtils;
import groovy.util.logging.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 百融还款结果回调
 */
@Slf4j
@Service
public class BairongCallbackService {

    private static final Logger logger = LoggerFactory.getLogger(BairongCallbackService.class);
    public static final long SEVEN = 7L;

    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private BairongConfig bairongConfig;
    @Autowired
    private LoanFailFollowRepository loanFailFollowRepository;
    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;


    /**
     * 资方授信失败、放款成功、放款失败则mq调用放款结果回调接口通知百融放款结果
     *
     * @param orderId 订单主键
     */
    public void bairongLoanResultCallbackNotice(String orderId) throws HttpException, JsonProcessingException {
        logger.info("放款结果回调接口通知百融放款结果: 订单order表记录id: [{}]", orderId);
        // 根据订单表id查询订单数据
        Order order = orderRepository.findById(orderId).orElseThrow(null);
        if (Objects.nonNull(order)) {
            logger.info("发起放款结果回调接口通知百融放款结果开始: 订单order表记录id: [{}]", orderId);
            Loan loan = loanRepository.findByOrderId(order.getId());
            // 订单状态转换
            BairongLoanStatus bairongLoanStatus = BairongConvert.toBairongLoanStatusByNewFlow(order.getOrderState());
            // 百融放款结果回调请求接口参数组装
            BairongLoanResultCallbackNoticeRequest request = new BairongLoanResultCallbackNoticeRequest();
            request.setOutLoanSeq(order.getOuterOrderId());
            if (BairongLoanStatus.LOAN_FAIL.equals(bairongLoanStatus)) {
                // 放款失败
                request.setLoanSeq(order.getId());
                request.setDnSts(bairongLoanStatus.getCode());
                request.setDnAmt(order.getApproveAmount());
                if (order.getRemark() != null && order.getRemark().contains(BairongResultCode.REMARK_MSG_INFO.getMsg())) {
                    request.setPayMsg(order.getRemark());
                } else if (order.getRemark() != null && order.getRemark().contains(BairongResultCode.REMARK_MSG_AMOUNT_INFO.getMsg())) {
                    request.setPayMsg(order.getRemark());
                } else {
                    if (Objects.nonNull(loan)) {
                        loanFailFollowRepository.findByLoanId(loan.getId()).ifPresent(follow -> {
                            request.setPayMsg(follow.getFailReason());
                        });
                    }
                }
            } else if (BairongLoanStatus.LOAN_PASS.equals(bairongLoanStatus)) {
                // 放款成功
                request.setLoanSeq(loan.getLoanRecordId());
                request.setContractNo(loan.getCreditId());
                request.setLoanNo(loan.getId());
                request.setDnSts(bairongLoanStatus.getCode());
                request.setDnAmt(loan.getAmount());
                request.setLoanActvDt(loan.getLoanTime().format(DateTimeFormatter.ofPattern(DateTimeUtils.PATTERN_DATE)));
                request.setLoanActvTime(loan.getLoanTime().format(DateTimeFormatter.ofPattern(DateTimeUtils.PATTERN_DATETIME)));
                request.setPayMsg(bairongLoanStatus.getDesc());
                request.setPriceIntRat(order.getIrrRate());
                request.setCustRate(order.getIrrRate());
            } else {
                // 放款中
                request.setLoanSeq(order.getId());
                request.setDnSts(bairongLoanStatus.getCode());
                request.setDnAmt(order.getApproveAmount());
                request.setPayMsg(bairongLoanStatus.getDesc());
            }
            //todo 数据是否做加密加签处理
            logger.info("调用百融：放款结果回调通知开始, 请求参数: [{}]", JSON.toJSONString(request));
            // 调用百融API：放款结果回调通知
            String res = HttpUtil.post(bairongConfig.getCallbackUrl() + BairongCallbackPathEnum.LOAN_RESULT_CALLBACK_PATH.getTradeCode(), JSON.toJSONString(request));
            JSONObject riskResult = JsonUtil.convertToObject(res, JSONObject.class);
            logger.info("调用百融：放款结果回调通知返回数据： [{}]", JSON.toJSONString(riskResult));
        }
    }

    /**
     * 还款结果回调接口通知百融还款结果
     *
     * @param loanId 借据号的ID
     * @throws HttpException
     * @throws JsonProcessingException
     */
    public void bairongRepayResultCallbackNotice(String loanId){
        logger.info("开始为借据ID [{}] 聚合还款记录并发送回调通知", loanId);
        Loan loan = loanRepository.findById(loanId).orElse(null);
        if (loan == null) {
            logger.error("未找到借据信息, 借据ID: {}", loanId);
            return;
        }
        Order order = orderRepository.findById(loan.getOrderId()).orElse(null);
        if (order == null) {
            logger.error("未找到借据关联的订单信息, 借据ID: {}", loanId);
            return;
        }
        List<CustomRepayRecord> customRepayRecordList = customRepayRecordRepository.findByLoanId(loanId);
        if (customRepayRecordList == null || customRepayRecordList.isEmpty()) {
            logger.warn("借据ID [{}] 没有找到任何相关的还款记录，回调流程结束。", loanId);
            return;
        }

        List<RepaymentDetailDto> repaymentDetails = customRepayRecordList.stream()
            .map(RepaymentDetailDto::from)
            .collect(Collectors.toList());

        BigDecimal totalRepayAmount = customRepayRecordList.stream()
            .map(CustomRepayRecord::getTotalAmt)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BairongRepayStatus overallStatus = determineOverallStatus(customRepayRecordList);

        LocalDateTime latestRepayDate = customRepayRecordList.stream()
            .map(CustomRepayRecord::getRepaidDate)
            .filter(Objects::nonNull)
            .max(LocalDateTime::compareTo)
            .orElse(LocalDateTime.now());

        CustomRepayRecord representativeRecord = customRepayRecordList.get(0);

        BairongRepayCallbackRequest request = new BairongRepayCallbackRequest();
        request.setLoanNo(loan.getId());
        // 使用借据ID
        request.setSetlSeq("BATCH_" + loan.getId() + "_" + System.currentTimeMillis());
        request.setOutBatchRepaymentSeq(representativeRecord.getOuterRepayNo());
        request.setPpErInd(BairongConvert.toBairongRepayMethod(representativeRecord.getRepayMode()));

        request.setApplyRepayAmt(totalRepayAmount); // 使用计算出的总金额
        request.setCrtDt(latestRepayDate.format(DateTimeFormatter.ofPattern(DateTimeUtils.PATTERN_DATETIME))); // 使用最近的还款时间
        request.setBillStatus(overallStatus.getCode());

        if (overallStatus == BairongRepayStatus.FAIL) {
            // 如果整体失败，拼接所有失败原因
            String allFailReasons = customRepayRecordList.stream()
                .filter(r -> r.getRepayState() == ProcessState.FAILED)
                .map(CustomRepayRecord::getFailReason)
                .collect(Collectors.joining("; "));
            request.setFailReason(allFailReasons);
        } else {
            request.setFailReason(overallStatus.getDesc());
        }

        // 设置转换好的明细列表
        request.setRepaymentList(repaymentDetails);

        logger.info("调用百融：聚合还款结果回调通知开始, 借据ID [{}], 请求参数: [{}]", loanId, JSON.toJSONString(request));
        try {
            String res = HttpUtil.post(bairongConfig.getCallbackUrl() + BairongCallbackPathEnum.REPAY_RESULT_CALLBACK_PATH.getTradeCode(), JSON.toJSONString(request));
            JSONObject riskResult = JsonUtil.convertToObject(res, JSONObject.class);
            logger.info("调用百融：聚合还款结果回调通知成功, 借据ID [{}], 返回数据： [{}]", loanId, JSON.toJSONString(riskResult));
        } catch (Exception e) {
            logger.error("调用百融：聚合还款结果回调通知失败, 借据ID [{}], 错误信息: ", loanId, e);
        }
    }

    /**
     * 根据还款记录列表决定整体的回调状态
     */
    private BairongRepayStatus determineOverallStatus(List<CustomRepayRecord> records) {
        boolean hasFailure = records.stream().anyMatch(r -> r.getRepayState() == ProcessState.FAILED);
        boolean hasProcessing = records.stream().anyMatch(r -> r.getRepayState() == ProcessState.PROCESSING);

        if (hasFailure) {
            return BairongRepayStatus.FAIL;
        }
        if (hasProcessing) {
            return BairongRepayStatus.PROCESSING;
        }
        return BairongRepayStatus.SUCCESS;
    }
}
