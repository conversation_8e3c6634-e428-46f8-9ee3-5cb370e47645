package com.maguo.loan.cash.flow.entrance.bairong.dto.agreement;

import com.maguo.loan.cash.flow.entrance.bairong.exception.BairongResultCode;

import static com.maguo.loan.cash.flow.entrance.bairong.exception.BairongResultCode.OUTAPPLSEQ_OR_OUTLOANSEQ_IS_NULL_ERROR;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 百融协议查询请求DTO
 * @date 2025/9/16
 */
public class BairongAgreementQueryRequest {

    /**
     * 渠道授信流水号
     */
    private String outApplSeq;

    /**
     * 渠道放款流水号
     */
    private String outLoanSeq;

    /**
     * 业务阶段：1授信阶段 2放款阶段 3绑卡阶段 4全部
     */
    private String businessStage;

    public String getOutApplSeq() {
        return outApplSeq;
    }

    public void setOutApplSeq(String outApplSeq) {
        this.outApplSeq = outApplSeq;
    }

    public String getOutLoanSeq() {
        return outLoanSeq;
    }

    public void setOutLoanSeq(String outLoanSeq) {
        this.outLoanSeq = outLoanSeq;
    }

    public String getBusinessStage() {
        return businessStage;
    }

    public void setBusinessStage(String businessStage) {
        this.businessStage = businessStage;
    }

    /**
     * 验证请求参数
     * @return 验证结果消息，null表示验证通过
     */
    public BairongResultCode validate() {
        if ((outApplSeq == null || outApplSeq.trim().isEmpty()) &&
            (outLoanSeq == null || outLoanSeq.trim().isEmpty())) {
            return BairongResultCode.OUTAPPLSEQ_OR_OUTLOANSEQ_IS_NULL_ERROR;
        }
        return null;
    }
}
