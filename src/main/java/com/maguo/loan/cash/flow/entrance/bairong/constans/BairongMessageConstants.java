package com.maguo.loan.cash.flow.entrance.bairong.constans;

/**
 * 百融流程相关的提示信息常量
 */
public final class BairongMessageConstants {
    private BairongMessageConstants() {
    }

    // 基础参数校验
    public static final String PROJECT_CONFIG_NOT_FOUND = "未配置项目信息";
    public static final String CREDIT_DARK_HOURS = "当前时间在授信黑暗期内，请稍后再试";
    public static final String DAILY_LIMIT_EXCEEDED = "日授信限额超限";
    public static final String AGE_NOT_IN_RANGE = "年龄不在允许范围内";
    public static final String AMOUNT_NOT_IN_DRAWABLE_RANGE = "申请金额不在可提现范围内";
    public static final String IMAGE_FILES_EXIST = "申请金额不在可提现范围内";


    // 放款流程
    public static final String NO_VALID_CREDIT = "无有效授信，不可发起借款";
    public static final String LOAN_PARAM_VALIDATION_FAILED = "放款申请参数校验失败";
    public static final String UNSUPPORTED_BANK_CARD = "不支持的银行卡";
}
