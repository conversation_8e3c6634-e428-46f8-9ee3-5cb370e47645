package com.maguo.loan.cash.flow.entrance.bairong.enums;

/**
 * <AUTHOR>
 * @requirement: 百融回调地址
 * @description: 调用百融方的接口接口
 * @date 2025-9-15
 */
public enum BairongCallbackPathEnum {

    /**
     * 放款结果回调地址
     */
    LOAN_RESULT_CALLBACK_PATH("/api/loanResultNotify/BRYC", "放款结果回调接口"),

    /**
     * 还款结果回调地址
     */
    REPAY_RESULT_CALLBACK_PATH("/api/repayResultNotify/BRYC", "还款结果回调接口");


    /**
     * 接口地址
     */
    private final String tradeCode;

    /**
     * 接口地址描述
     */
    private final String tradeName;

    BairongCallbackPathEnum(String tradeCode, String tradeName) {
        this.tradeCode = tradeCode;
        this.tradeName = tradeName;
    }

    public String getTradeCode() {
        return tradeCode;
    }

    public String getTradeName() {
        return tradeName;
    }

}
