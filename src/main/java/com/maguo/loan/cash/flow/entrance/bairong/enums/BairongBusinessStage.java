package com.maguo.loan.cash.flow.entrance.bairong.enums;

import com.maguo.loan.cash.flow.enums.LoanStage;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/9/16 10:42
 */
public enum BairongBusinessStage {
    /**
     * 授信阶段
     */
    CREDIT("1", "授信阶段"),

    /**
     * 放款阶段
     */
    LOAN("2", "放款阶段"),

    /**
     * 绑卡阶段
     */
    BIND_CARD("3", "绑卡阶段"),

    /**
     * 全部
     */
    ALL("4", "全部");

    private final String code;
    private final String description;

    BairongBusinessStage(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取业务阶段
     *
     * @param code 代码
     * @return 业务阶段
     */
    public static BairongBusinessStage getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (BairongBusinessStage stage : BairongBusinessStage.values()) {
            if (stage.getCode().equals(code)) {
                return stage;
            }
        }
        return null;
    }

    /**
     * 根据LoanStage转换为BusinessStage
     *
     * @param loanStage 放款阶段
     * @return 业务阶段
     */
    public static BairongBusinessStage fromLoanStage(LoanStage loanStage) {
        if (loanStage == null) {
            return null;
        }
        return switch (loanStage) {
            case RISK -> CREDIT;
            case CREDIT -> CREDIT;
            case LOAN -> LOAN;
            default -> LOAN;
        };
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
