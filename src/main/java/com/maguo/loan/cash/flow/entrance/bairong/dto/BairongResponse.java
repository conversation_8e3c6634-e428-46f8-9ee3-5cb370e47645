package com.maguo.loan.cash.flow.entrance.bairong.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.maguo.loan.cash.flow.entrance.bairong.exception.BairongResultCode;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class BairongResponse<T> {
    private String code;
    private String message;
    private T data;

    public static <T> BairongResponse<T> success(T data) {
        BairongResponse result = new BairongResponse();
        result.setCode(BairongResultCode.SUCCESS.getCode());
        result.setMessage(BairongResultCode.SUCCESS.getMsg());
        return result;
    }


    public static BairongResponse fail() {
        BairongResponse result = new BairongResponse();
        result.setCode(BairongResultCode.SYSTEM_ERROR.getCode());
        result.setMessage(BairongResultCode.SYSTEM_ERROR.getMsg());
        return result;
    }

    public static BairongResponse fail(String message) {
        BairongResponse result = new BairongResponse();
        result.setCode(BairongResultCode.SYSTEM_ERROR.getCode());
        result.setMessage(message);
        return result;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
