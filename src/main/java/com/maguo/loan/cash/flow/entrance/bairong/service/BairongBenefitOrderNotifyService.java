package com.maguo.loan.cash.flow.entrance.bairong.service;

import com.maguo.loan.cash.flow.entity.bairong.BairongBenefitOrder;
import com.maguo.loan.cash.flow.entrance.bairong.convert.BairongConvert;
import com.maguo.loan.cash.flow.entrance.bairong.dto.callBack.BairongBenefitOrderNotifyRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.callBack.BairongBenefitOrderNotifyResponse;
import com.maguo.loan.cash.flow.entrance.bairong.dto.callBack.PartnerShareInfo;
import com.maguo.loan.cash.flow.entrance.bairong.dto.callBack.RefundInfo;
import com.maguo.loan.cash.flow.entrance.bairong.enums.BairongBenefitOrderNotifyResultCode;
import com.maguo.loan.cash.flow.entrance.bairong.exception.BairongException;
import com.maguo.loan.cash.flow.entrance.bairong.exception.BairongResultCode;
import com.maguo.loan.cash.flow.repository.bairong.BairongBenefitOrderRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 权益订单通知业务层接口
 */
@Service
public class BairongBenefitOrderNotifyService {

    private static final Logger logger = LoggerFactory.getLogger(BairongBenefitOrderNotifyService.class);

    @Autowired
    private BairongBenefitOrderRepository bairongBenefitOrderRepository;

    /**
     * 处理权益订单通知完整流程
     *
     * @param request 权益订单通知请求参数
     * @return 响应结果
     */
    public BairongBenefitOrderNotifyResponse receiveBenefitOrderNotify(BairongBenefitOrderNotifyRequest request) {
        logger.info("接收权益订单通知请求，请求参数{}：", request.toString());

        // 2. 初始化响应对象（默认设为接收失败，后续成功时更新）
        BairongBenefitOrderNotifyResponse response = new BairongBenefitOrderNotifyResponse();
        response.setResultCode(BairongBenefitOrderNotifyResultCode.FAILURE.getCode()); // 02：接收失败
        response.setResultDesc(BairongBenefitOrderNotifyResultCode.FAILURE.getMessage());

        try {
            // 3. 调用业务层方法：处理订单通知（如参数校验、数据存储、覆盖更新等）
            boolean handleSuccess = handleBenefitOrderNotify(request);

            // 4. 根据业务处理结果更新响应参数
            if (handleSuccess) {
                response.setResultCode(BairongBenefitOrderNotifyResultCode.SUCCESS.getCode()); // 01：接收成功
                response.setResultDesc(BairongBenefitOrderNotifyResultCode.SUCCESS.getMessage());
                logger.info("权益订单通知处理成功，loanNo={}, benefitOrderNo={}",
                    request.getLoanNo(), request.getBenefitOrderNo());
            } else {
                response.setResultDesc(BairongResultCode.EQUITY_ORDER_NOTIFY_FAIL.getMsg());
                logger.warn("权益订单通知业务处理失败，loanNo={}, benefitOrderNo={}",
                    request.getLoanNo(), request.getBenefitOrderNo());
            }

        } catch (Exception e) {
            // 5. 异常处理：捕获处理过程中的异常，记录日志并返回失败响应
            logger.error("接收权益订单通知异常，请求参数：{}，异常信息：", request, e);
            response.setResultDesc(e.getMessage());
        }

        // 6. 返回响应结果给百融
        return response;
    }

    /**
     * 处理权益订单通知
     *
     * @param request 权益订单通知请求参数
     * @return 处理结果（true：处理成功，false：处理失败）
     */
    public boolean handleBenefitOrderNotify(BairongBenefitOrderNotifyRequest request) {
        try {
            // 校验请求参数
            validateRequest(request);
            // 处理权益订单的具体逻辑
            return processBenefitOrder(request);
        } catch (BairongException e) {
            logger.error("处理权益订单通知参数校验失败: outLoanSeq={}, errorMessage={}",
                request != null ? request.getOutLoanSeq() : "null", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            logger.error("处理权益订单通知发生未知异常: outLoanSeq={}, errorMessage={}",
                request != null ? request.getOutLoanSeq() : "null", e.getMessage(), e);
            throw new BairongException(BairongResultCode.EQUITY_ORDER_NOTIFY_FAIL);
        }
    }

    /**
     * 处理权益订单的具体逻辑
     *
     * @param request 权益订单通知请求参数
     * @return 处理结果（true：处理成功，false：处理失败）
     */
    private boolean processBenefitOrder(BairongBenefitOrderNotifyRequest request) {
        try {
            // 根据outLoanSeq查询是否已存在记录
            BairongBenefitOrder bairongBenefitOrder = bairongBenefitOrderRepository.findByOutLoanSeq(request.getOutLoanSeq());
            boolean isCurrentFinalStatus = isFinalStatus(request.getStatus());

            if (Objects.isNull(bairongBenefitOrder)) {
                // 1. 表中没有记录，就初始化数据
                logger.info("权益订单通知：未找到订单记录，创建新记录，outLoanSeq={}", request.getOutLoanSeq());
                BairongBenefitOrder order = new BairongBenefitOrder();

                BairongConvert.INSTANCE.toBairongBenefitOrder(order, request);
                // 处理PartnerShareInfo和RefundInfo列表数据
                populateAdditionalInfo(order, request);
                bairongBenefitOrderRepository.save(order);
            } else if (isCurrentFinalStatus) {
                // 表中已有记录、且终态
                logger.info("权益订单通知：更新终态订单，outLoanSeq={}", request.getOutLoanSeq());
                BairongConvert.INSTANCE.toBairongBenefitOrder(bairongBenefitOrder, request);
                bairongBenefitOrderRepository.save(bairongBenefitOrder);
            }
            return true;
        } catch (Exception e) {
            logger.error("处理权益订单通知失败: outLoanSeq={}, errorMessage={}",
                request != null ? request.getOutLoanSeq() : "null", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 填充额外信息（PartnerShareInfo和RefundInfo列表数据）
     *
     * @param order   权益订单实体
     * @param request 权益订单通知请求参数
     */
    private void populateAdditionalInfo(BairongBenefitOrder order, BairongBenefitOrderNotifyRequest request) {
        // 处理PartnerShareInfo列表，取第一个元素（如果存在）
        if (Objects.nonNull(request.getPartnerShareList()) && !request.getPartnerShareList().isEmpty()) {
            PartnerShareInfo partnerShareInfo = request.getPartnerShareList().get(0);
            order.setPartnerShareMerchantNo(partnerShareInfo.getPartnerShareMerchantNo());
            order.setPartnerShareAmount(partnerShareInfo.getPartnerShareAmount());
            order.setPartnerShareRefundAmount(partnerShareInfo.getPartnerShareRefundAmount());
        }

        // 处理RefundInfo列表，取第一个元素（如果存在）
        if (Objects.nonNull(request.getRefundInfoList()) && !request.getRefundInfoList().isEmpty()) {
            RefundInfo refundInfo = request.getRefundInfoList().get(0);
            order.setRefundNo(refundInfo.getRefundNo());
            order.setRefundTime(refundInfo.getRefundTime());
            order.setRefundAmount(refundInfo.getRefundAmount());
            order.setRefundMethod(refundInfo.getRefundMethod());
        }
    }

    /**
     * 校验请求参数
     *
     * @param request 权益订单通知请求参数
     * @throws BairongException 参数校验失败异常
     */
    private void validateRequest(BairongBenefitOrderNotifyRequest request) throws BairongException {
        if (Objects.isNull(request)) {
            throw new BairongException(BairongResultCode.INVALID_PARAM);
        }
        if (Objects.isNull(request.getOutLoanSeq()) || request.getOutLoanSeq().trim().isEmpty()) {
            throw new BairongException(BairongResultCode.OUT_LOAN_SEQ_CAN_NOT_BE_NULL);
        }
        if (Objects.isNull(request.getStatus())) {
            logger.error("权益订单通知参数校验失败：订单状态不能为空，outLoanSeq={}", request.getOutLoanSeq());
            throw new BairongException(BairongResultCode.INVALID_PARAM);
        }
    }

    /**
     * 判断订单状态是否为终态
     *
     * @param status 订单状态
     * @return true-是终态，false-不是终态
     */
    private boolean isFinalStatus(Integer status) {
        // 2-支付成功, 3-支付失败, 5-退款成功, 6-退款失败, 7-订单取消 是终态
        return status != null && (status == 2 || status == 3 || status == 5 || status == 6 || status == 7);
    }
}
