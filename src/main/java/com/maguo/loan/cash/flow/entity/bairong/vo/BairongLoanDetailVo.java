package com.maguo.loan.cash.flow.entity.bairong.vo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 百融放款明细
 * @date 2025/9/15 9:14
 */
public class BairongLoanDetailVo {


    /**
     * 渠道放款流水号
     */
    private String outApplSeq;

    /**
     * 用款申请日期 yyyy-MM-dd
     */
    private String applyDt;

    /**
     * 借据号
     */
    private String loanNo;

    /**
     * 放款金额
     */
    private BigDecimal dnAmt;

    /**
     * 申请期限
     */
    private Integer applyTnr;

    /**
     * 年化利率
     */
    private BigDecimal basicIntRat;

    /**
     * 放款时间 yyyy-MM-dd
     */
    private String loanActvDt;

    /**
     * 放款账户姓名
     */
    private String acctName;

    /**
     * 放款账户开户行
     */
    private String acctBank;

    /**
     * 放款账号
     */
    private String acctNo;

    // Getter和Setter方法
    public String getOutApplSeq() {
        return outApplSeq;
    }

    public void setOutApplSeq(String outApplSeq) {
        this.outApplSeq = outApplSeq;
    }

    public String getApplyDt() {
        return applyDt;
    }

    public void setApplyDt(String applyDt) {
        this.applyDt = applyDt;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public BigDecimal getDnAmt() {
        return dnAmt;
    }

    public void setDnAmt(BigDecimal dnAmt) {
        this.dnAmt = dnAmt;
    }

    public Integer getApplyTnr() {
        return applyTnr;
    }

    public void setApplyTnr(Integer applyTnr) {
        this.applyTnr = applyTnr;
    }

    public BigDecimal getBasicIntRat() {
        return basicIntRat;
    }

    public void setBasicIntRat(BigDecimal basicIntRat) {
        this.basicIntRat = basicIntRat;
    }

    public String getLoanActvDt() {
        return loanActvDt;
    }

    public void setLoanActvDt(String loanActvDt) {
        this.loanActvDt = loanActvDt;
    }

    public String getAcctName() {
        return acctName;
    }

    public void setAcctName(String acctName) {
        this.acctName = acctName;
    }

    public String getAcctBank() {
        return acctBank;
    }

    public void setAcctBank(String acctBank) {
        this.acctBank = acctBank;
    }

    public String getAcctNo() {
        return acctNo;
    }

    public void setAcctNo(String acctNo) {
        this.acctNo = acctNo;
    }

    // toString方法
    @Override
    public String toString() {
        return "LoanInfoVO{" +
            "outApplSeq='" + outApplSeq + '\'' +
            ", applyDt='" + applyDt + '\'' +
            ", loanNo='" + loanNo + '\'' +
            ", dnAmt=" + dnAmt +
            ", applyTnr=" + applyTnr +
            ", basicIntRat=" + basicIntRat +
            ", loanActvDt='" + loanActvDt + '\'' +
            ", acctName='" + acctName + '\'' +
            ", acctBank='" + acctBank + '\'' +
            ", acctNo='" + acctNo + '\'' +
            '}';
    }
}
