package com.maguo.loan.cash.flow.controller;

import com.jinghang.ppd.api.OrderApi;
import com.jinghang.ppd.api.dto.RestResult;
import com.jinghang.ppd.api.dto.ResultCode;
import com.jinghang.ppd.api.dto.order.OrderParamDto;
import com.jinghang.ppd.api.dto.order.OrderQueryRequestDto;
import com.jinghang.ppd.api.dto.order.OrderQueryResponseDto;
import com.maguo.loan.cash.flow.convert.ManageConvert;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.service.OrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("manage/order")
public class ManageOrderController implements OrderApi {
    private static final Logger logger = LoggerFactory.getLogger(ManageOrderController.class);

    @Autowired
    private OrderService orderService;

    @Override
    public RestResult<Void> cancel(OrderParamDto orderParamDto) {
        orderService.cancel(orderParamDto.getOrderId(), orderParamDto.getUpdateBy());
        return RestResult.success(null);
    }

    public RestResult<OrderQueryResponseDto> queryByOrderId(OrderQueryRequestDto requestDto) {
        if (requestDto == null || requestDto.getOrderId() == null || requestDto.getOrderId().isEmpty()) {
            return RestResult.fail(ResultCode.PARAM_ILLEGAL,  ResultCode.PARAM_ILLEGAL.getMsg());
        }

        logger.info("开始查询订单, orderId: {}", requestDto.getOrderId());

        Order order = orderService.findById(requestDto.getOrderId());

        if (order == null) {
            logger.warn("订单不存在, orderId: {}", requestDto.getOrderId());
            return RestResult.fail(ResultCode.PARAM_ILLEGAL, ResultCode.PARAM_ILLEGAL.getMsg());
        }

        OrderQueryResponseDto responseDto = ManageConvert.INSTANCE.toOrderQueryResponseDto(order);

        return RestResult.success(responseDto);
    }

}
