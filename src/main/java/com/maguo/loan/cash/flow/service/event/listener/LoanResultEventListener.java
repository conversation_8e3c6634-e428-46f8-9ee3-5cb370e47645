package com.maguo.loan.cash.flow.service.event.listener;


import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.dto.CallBackDTO;
import com.maguo.loan.cash.flow.entity.BairongEquityFlow;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entity.bairong.BairongBenefitOrder;
import com.maguo.loan.cash.flow.enums.CallbackState;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.repository.bairong.BairongBenefitOrderRepository;
import com.maguo.loan.cash.flow.repository.bairong.BairongEquityFlowRepository;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.OrderRouterService;
import com.maguo.loan.cash.flow.service.RepayPlanService;
import com.maguo.loan.cash.flow.service.agreement.AgreementService;
import com.maguo.loan.cash.flow.service.event.LoanResultEvent;
import com.maguo.loan.cash.flow.service.event.OrderCancelEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/10/14
 */
@Component
public class LoanResultEventListener {

    private static final Logger logger = LoggerFactory.getLogger(LoanResultEventListener.class);

    private LoanRepository loanRepository;

    private OrderRepository orderRepository;
    /**
     * 百融权益流水信息表
     */
    private BairongEquityFlowRepository bairongEquityFlowRepository;
    /**
     * 权益订单表
     */
    private BairongBenefitOrderRepository bairongBenefitOrderRepository;
    private RepayPlanService repayPlanService;

    private MqService mqService;

    @Autowired
    private AgreementService agreementService;

    @Autowired
    private OrderRouterService orderRouterService;

    @Autowired
    private PreOrderRepository preOrderRepository;

    @EventListener(LoanResultEvent.class)
    public void onApplicationEvent(LoanResultEvent loanResultEvent) {
        logger.info("处理放款事件:{}", JsonUtil.toJsonString(loanResultEvent));
        Loan loan = loanRepository.findById(loanResultEvent.getLoanId()).orElseThrow(() -> new BizException(ResultCode.LOAN_NOT_EXIST));
        Order order = orderRepository.findById(loan.getOrderId()).orElseThrow(() -> new BizException(ResultCode.ORDER_NOT_EXIST));
        ProcessState loanState = loanResultEvent.getLoanState();
        if (ProcessState.SUCCEED.equals(loanState)) {
            // 还款计划
            List<RepayPlan> repayPlans = repayPlanService.generateRepayPlan(loan);
            RepayPlan firstRepayPlan = repayPlans.stream().filter(s -> s.getPeriod() == 1).findFirst().orElseThrow();
            // order订单状态
            order.setOrderState(OrderState.LOAN_PASS);
            order.setLoanTime(loanResultEvent.getLoanTime());
            orderRepository.save(order);
            // 协议签署
            agreementService.applySign(loan.getOrderId(), LoanStage.LOAN, loan.getProjectCode());
            if (FlowChannel.FQLQY001.equals(loan.getFlowChannel())){
                //放款成功 通知百维
                mqService.submitBaiWeiRiskNotify(order.getRiskId());
            }
            // 百融-超捷-长银资方放款成功之后需要新增落库无终态权益流水表信息数据
            if (FlowChannel.LTFQ.equals(loan.getFlowChannel())) {
                BairongEquityFlow bairongEquityFlow = initBairongEquityFlowData(loan.getId(), order.getOuterOrderId(), loanResultEvent.getLoanTime());
                // 需要查询权益终态那边是否拉取到数据；如果拉取到则更新权益订单号、是否获取字段
                // 根据渠道放款流水号查询权益订单数据是否获取到，如果获取到则更新权益订单号、是否获取字段，否组不做更新只初始化数据；
                BairongBenefitOrder benefitOrder = bairongBenefitOrderRepository.findByOutLoanSeq(order.getOuterOrderId());
                if (Objects.nonNull(benefitOrder)) {
                    bairongEquityFlow.setRightsOrderNo(benefitOrder.getBenefitOrderNo());
                    bairongEquityFlow.setIsObtain(WhetherState.Y);
                }
                bairongEquityFlowRepository.save(bairongEquityFlow);
                // 放款结果回调通知百融
                mqService.submitBairongLoanCallbackNotice(order.getId());
            }
        }

        if (ProcessState.FAILED.equals(loanState)) {
            order.setOrderState(OrderState.LOAN_FAIL);
            order.setRemark(loanResultEvent.getFailReason());
            orderRepository.save(order);
            if (FlowChannel.FQLQY001.equals(loan.getFlowChannel())){
                //放款失败 通知百维
                mqService.submitBaiWeiRiskNotify(order.getRiskId());
            }
            // 放款结果回调通知百融
            if (FlowChannel.LTFQ.equals(loan.getFlowChannel())) {
                // 放款结果回调通知百融
                mqService.submitBairongLoanCallbackNotice(order.getId());
            }
        }
        //回调流量
        //取消订单路由
        orderRouterService.cancelOrderRouter(order.getId(), "放款" + (ProcessState.SUCCEED.equals(loanState) ? "成功" : "失败") + ",取消本条路由");
    }


    @EventListener(OrderCancelEvent.class)
    public void onCancelEvent(OrderCancelEvent loanResultEvent) {
        logger.info("处理放款取消/失败事件:{}", JsonUtil.toJsonString(loanResultEvent));

        Order order = orderRepository.findById(loanResultEvent.getOrderId()).orElseThrow(() -> new BizException(ResultCode.ORDER_NOT_EXIST));
        order.setOrderState(OrderState.LOAN_FAIL);
        order.setRemark(loanResultEvent.getRemark());
        orderRepository.save(order);

        Loan loan = loanRepository.findByOrderId(order.getId());
        if (Objects.nonNull(loan)) {
            loan.setLoanState(ProcessState.FAILED);
            loan.setFailReason("二次风控拒绝");
            loanRepository.save(loan);
        }

        CallBackDTO callBackDTO = new CallBackDTO();
        callBackDTO.setBusinessId(order.getId());
        callBackDTO.setFlowChannel(order.getFlowChannel());
        callBackDTO.setCallbackState(CallbackState.LOAN_FAIL);
        callBackDTO.setRemark(loanResultEvent.getRemark());
        mqService.submitCallbackCommonNotify(JsonUtil.toJsonString(callBackDTO));
    }


    private void repayPlanSyncProcess(Loan loan) {
        logger.info("放款成功后 向fin-core同步还款计划 loanId:{}", loan.getId());
        // 同步资方还款计划
        mqService.submitRepayPlanSync(loan.getId());
    }

    /**
     * 放款成功之后初始化百融权益流水信息数据
     *
     * @param loanId       借据主键
     * @param outerOrderId 外部订单号
     * @param loanTime     放款成功时间
     * @return 返回数据
     */
    private BairongEquityFlow initBairongEquityFlowData(String loanId, String outerOrderId, LocalDateTime loanTime) {
        BairongEquityFlow bairongEquityFlow = new BairongEquityFlow();
        bairongEquityFlow.setLoanId(loanId);
        bairongEquityFlow.setOutLoanSeq(outerOrderId);
        bairongEquityFlow.setIsObtain(WhetherState.N);
        bairongEquityFlow.setPullFrequency(BigDecimal.ZERO.intValue());
        bairongEquityFlow.setLoanTime(loanTime);
        return bairongEquityFlow;
    }

    @Autowired
    public void setLoanRepository(LoanRepository loanRepository) {
        this.loanRepository = loanRepository;
    }

    @Autowired
    public void setOrderRepository(OrderRepository orderRepository) {
        this.orderRepository = orderRepository;
    }

    @Autowired
    public void setRepayPlanService(RepayPlanService repayPlanService) {
        this.repayPlanService = repayPlanService;
    }

    @Autowired
    public void setMqService(MqService mqService) {
        this.mqService = mqService;
    }

    @Autowired
    public void setBairongEquityFlowRepository(BairongEquityFlowRepository bairongEquityFlowRepository) {
        this.bairongEquityFlowRepository = bairongEquityFlowRepository;
    }

    @Autowired
    public void setBairongBenefitOrderRepository(BairongBenefitOrderRepository bairongBenefitOrderRepository) {
        this.bairongBenefitOrderRepository = bairongBenefitOrderRepository;
    }

}
