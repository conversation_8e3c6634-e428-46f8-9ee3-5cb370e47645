package com.maguo.loan.cash.flow.entrance.bairong.enums;

/**
 * <AUTHOR>
 * @Description 百融放款状态
 * @Date 2025/9/11 14:47
 * @Version v1.0
 **/
public enum BairongLoanStatus {

    LOANING("100", "放款中"),
    LOAN_PASS("200", "放款成功"),
    LOAN_FAIL("300", "放款失败"),
    LOAN_FAILED("300", "授信超过有效期"),
    LOAN_ABNORMAL("300", "放款状态查询异常"),
    NO_SUCH_ORDER("500", "查无此单"),
    ;

    private String code;
    private String desc;

    BairongLoanStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
