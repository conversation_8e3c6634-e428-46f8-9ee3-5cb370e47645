package com.maguo.loan.cash.flow.entrance.bairong.dto.agreement;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 百融协议查询响应DTO
 * @date 2025/9/16
 */
public class BairongAgreementQueryResponse {

    /**
     * 协议列表
     */
    private List<ContractInfo> contractList;

    public List<ContractInfo> getContractList() {
        return contractList;
    }

    public void setContractList(List<ContractInfo> contractList) {
        this.contractList = contractList;
    }

    /**
     * 协议信息
     */
    public static class ContractInfo {
        /**
         * 影像文件（字节数组）
         */
        private byte[] data;

        /**
         * 文件名
         */
        private String fileName;

        /**
         * 协议类型（取值为5.28协议类型码表中的"协议类型"）
         */
        private String imageType;

        public byte[] getData() {
            return data;
        }

        public void setData(byte[] data) {
            this.data = data;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public String getImageType() {
            return imageType;
        }

        public void setImageType(String imageType) {
            this.imageType = imageType;
        }
    }
}
