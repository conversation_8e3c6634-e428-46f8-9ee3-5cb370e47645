package com.maguo.loan.cash.flow.entity.bairong;

import com.maguo.loan.cash.flow.entity.BaseEntity;
import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.ProcessState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/9/16 10:34
 */
@Setter
@Getter
@Entity
@Table(name = "bairong_agreement_upload_record")
public class BairongAgreementUploadRecord extends BaseEntity {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 借据ID
     */
    private String loanId;

    /**
     * 渠道授信流水号
     */
    private String outApplSeq;

    /**
     * 渠道放款流水号
     */
    private String outLoanSeq;

    /**
     * 业务阶段：1授信阶段 2放款阶段 3绑卡阶段
     */
    private String businessStage;

    /**
     * 协议类型码（对应5.28协议类型码表中的"协议类型"）
     */
    private String imageType;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件类型
     */
    @Enumerated(EnumType.STRING)
    private FileType fileType;

    /**
     * 放款阶段
     */
    @Enumerated(EnumType.STRING)
    private LoanStage loanStage;

    /**
     * OSS桶名
     */
    private String ossBucket;

    /**
     * OSS文件key
     */
    private String ossKey;

    /**
     * SFTP上传路径
     */
    private String sftpPath;

    /**
     * 上传状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessState uploadStatus;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 项目代码
     */
    private String projectCode;

    /**
     * 外部借据ID
     */
    private String outerLoanId;

    @Override
    protected String prefix() {
        return "BAR";
    }
}
