package com.maguo.loan.cash.flow.enums;


import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum FlowChannel {

    LVXIN("绿信", FlowType.IRR36, "绿信"),
    TONG_CHENG("同程", FlowType.IRR36, "同程"),
    PPCJDL("信也--拍拍", FlowType.IRR36, "拍拍贷"),
    FQLQY001("分期乐", FlowType.IRR24, "分期乐"),
    LTFQ("乐通分期", FlowType.IRR24, "乐通分期"),
    ;

    private final String desc;

    /**
     * 流量类型
     */
    private final FlowType flowType;

    /**
     * 流量类型
     */
    private final String appName;

    private static final List<FlowChannel> COMMON_CHANNEL =
        List.of();

    public static final List<FlowChannel> IRR36_RIGHTS_CHANNELS =
        Arrays.stream(FlowChannel.values()).filter(s -> s.flowType == FlowType.IRR36_RIGHTS).collect(Collectors.toList());

    private static final List<FlowChannel> REPAY_NO_SEND_SMS = List.of();
    private static final List<FlowChannel> LOAN_NO_SEND_SMS = List.of();

    FlowChannel(String desc, FlowType flowType, String appName) {
        this.desc = desc;
        this.flowType = flowType;
        this.appName = appName;
    }
    public static boolean isRepaySendSms(FlowChannel flowChannel) {
        return !REPAY_NO_SEND_SMS.contains(flowChannel);
    }

    public static boolean isLoanSendSms(FlowChannel flowChannel) {
        return !LOAN_NO_SEND_SMS.contains(flowChannel);
    }

    public static boolean isCommonApi(FlowChannel flowChannel) {
        return COMMON_CHANNEL.contains(flowChannel);
    }

    public String getDesc() {
        return desc;
    }

    public FlowType getFlowType() {
        return flowType;
    }

    public String getAppName() {
        return appName;
    }

    public static FlowChannel getFlowChannel(String name) {
        for (FlowChannel flowChannel : FlowChannel.values()) {
            if (flowChannel.name().equals(name)) {
                return flowChannel;
            }
        }
        return null;
    }

}
