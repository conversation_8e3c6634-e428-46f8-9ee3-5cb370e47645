package com.maguo.loan.cash.flow.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

import java.math.BigDecimal;

@Entity
@Table(name = "bairong_loan_apply")
public class BaiRongLoanApply extends BaseEntity {

    @Column(name = "out_loan_seq", length = 32, nullable = false)
    private String outLoanSeq; // 渠道放款流水号

    @Column(name = "apply_basic_info_id", length = 32)
    private String applyBasicInfoId; // 申请人信息表主键

    @Column(name = "appl_cde", length = 20)
    private String applCde; // 资金方授信流水号

    @Column(name = "apply_dt", length = 10, nullable = false)
    private String applyDt; // 申请日期

    @Column(name = "loan_typ", length = 20, nullable = false)
    private String loanTyp; // 贷款品种

    @Column(name = "dn_amt", precision = 16, scale = 2, nullable = false)
    private BigDecimal dnAmt; // 申请放款金额

    @Column(name = "apply_tnr", nullable = false)
    private Integer applyTnr; // 申请期数

    @Column(name = "mtd_cde", length = 20, nullable = false)
    private String mtdCde; // 还款方式

    @Column(name = "purpose", length = 20, nullable = false)
    private String purpose; // 贷款用途

    @Column(name = "other_purpose", length = 20)
    private String otherPurpose; // 其他贷款用途

    @Column(name = "price_int_rat", precision = 16, scale = 9)
    private BigDecimal priceIntRat; // 利息利率

    @Column(name = "cust_rate", precision = 16, scale = 9)
    private BigDecimal custRate; // 对客利率

    @Column(name = "due_day_opt", length = 20, nullable = false)
    private String dueDayOpt; // 每期还款日

    @Column(name = "due_day", length = 20)
    private String dueDay; // 还款日（固定日时必录）

    @Column(name = "direct_flag", length = 60, nullable = false)
    private String directFlag; // 断直连标识

    @Column(name = "basic_info_id", length = 32, nullable = false)
    private String basicInfoId; // 关联申请人ID

    public String getOutLoanSeq() {
        return outLoanSeq;
    }

    public void setOutLoanSeq(String outLoanSeq) {
        this.outLoanSeq = outLoanSeq;
    }

    public String getApplyBasicInfoId() {
        return applyBasicInfoId;
    }

    public void setApplyBasicInfoId(String applyBasicInfoId) {
        this.applyBasicInfoId = applyBasicInfoId;
    }

    public String getApplCde() {
        return applCde;
    }

    public void setApplCde(String applCde) {
        this.applCde = applCde;
    }

    public String getApplyDt() {
        return applyDt;
    }

    public void setApplyDt(String applyDt) {
        this.applyDt = applyDt;
    }

    public String getLoanTyp() {
        return loanTyp;
    }

    public void setLoanTyp(String loanTyp) {
        this.loanTyp = loanTyp;
    }

    public BigDecimal getDnAmt() {
        return dnAmt;
    }

    public void setDnAmt(BigDecimal dnAmt) {
        this.dnAmt = dnAmt;
    }

    public Integer getApplyTnr() {
        return applyTnr;
    }

    public void setApplyTnr(Integer applyTnr) {
        this.applyTnr = applyTnr;
    }

    public String getMtdCde() {
        return mtdCde;
    }

    public void setMtdCde(String mtdCde) {
        this.mtdCde = mtdCde;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public String getOtherPurpose() {
        return otherPurpose;
    }

    public void setOtherPurpose(String otherPurpose) {
        this.otherPurpose = otherPurpose;
    }

    public BigDecimal getPriceIntRat() {
        return priceIntRat;
    }

    public void setPriceIntRat(BigDecimal priceIntRat) {
        this.priceIntRat = priceIntRat;
    }

    public BigDecimal getCustRate() {
        return custRate;
    }

    public void setCustRate(BigDecimal custRate) {
        this.custRate = custRate;
    }

    public String getDueDayOpt() {
        return dueDayOpt;
    }

    public void setDueDayOpt(String dueDayOpt) {
        this.dueDayOpt = dueDayOpt;
    }

    public String getDueDay() {
        return dueDay;
    }

    public void setDueDay(String dueDay) {
        this.dueDay = dueDay;
    }

    public String getDirectFlag() {
        return directFlag;
    }

    public void setDirectFlag(String directFlag) {
        this.directFlag = directFlag;
    }

    public String getBasicInfoId() {
        return basicInfoId;
    }

    public void setBasicInfoId(String basicInfoId) {
        this.basicInfoId = basicInfoId;
    }
}

