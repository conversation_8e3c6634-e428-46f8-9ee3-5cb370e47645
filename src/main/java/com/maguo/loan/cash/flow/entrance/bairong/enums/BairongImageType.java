package com.maguo.loan.cash.flow.entrance.bairong.enums;

import com.maguo.loan.cash.flow.enums.FileType;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 百融协议类型码表枚举
 * @date 2025/9/16 11:30
 */
public enum BairongImageType {
    
    /**
     * 人行征信查询授权书
     */
    CREDIT_PBC_PROTOCOL("1", "人行征信查询授权书", "credit-pbc-protocol", FileType.PERSONAL_CREDIT_AUTHORIZATION_LETTER_CREDIT),
    
    /**
     * 担保-人行征信查询授权书
     */
    GUARANTEE_CREDIT_PBC_PROTOCOL("2", "担保-人行征信查询授权书", "guarantee-credit-pbc-protocol", FileType.PERSONAL_CREDIT_AUTHORIZATION_LETTER_CREDIT),
    
    /**
     * 个人信息授权书
     */
    CREDIT_PERSON_PROTOCOL("3", "个人信息授权书", "credit-person-protocol", FileType.PERSONAL_INFORMATION_AUTHORIZATION_LETTER),
    
    /**
     * 担保-个人信息授权书
     */
    GUARANTEE_CREDIT_PERSON_PROTOCOL("4", "担保-个人信息授权书", "guarantee-credit-person-protocol", FileType.PERSONAL_INFORMATION_AUTHORIZATION_LETTER),
    
    /**
     * 个人借款合同
     */
    BORROW_CONTRACT("5", "个人借款合同", "borrow-contract", FileType.LOAN_CONTRACT),
    
    /**
     * 担保合同
     */
    GUARANTEE_CONTRACT("6", "担保合同", "guarantee-contract", FileType.DEBT_CONFIRMATION_AGREEMENT),
    
    /**
     * 个人扣款授权协议
     */
    WITHDRAW_PROTOCOL("7", "个人扣款授权协议", "withdraw-protocal", FileType.ENTRUSTED_DEDUCTION_LETTER),
    
    /**
     * 担保-个人扣款授权协议
     */
    GUARANTEE_WITHDRAW_PROTOCOL("8", "担保-个人扣款授权协议", "guarantee-withdraw-protocal", FileType.AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE),
    
    /**
     * 综合授权书
     */
    CREDIT_MIX_PROTOCOL("9", "综合授权书", "credit-mix-protocol", FileType.SYNTHESIS_AUTHORIZATION),
    
    /**
     * 担保-综合授权书
     */
    GUARANTEE_CREDIT_MIX_PROTOCOL("10", "担保-综合授权书", "guarantee-credit-mix-protocol", FileType.COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE),
    
    /**
     * 授信额度协议
     */
    CREDIT_CONTRACT("11", "授信额度协议", "credit-contract", FileType.LOAN_CONTRACT),
    
    /**
     * 数字证书使用协议
     */
    DIGITAL_CERTIFICATE("12", "数字证书使用协议", "digital-certiface", FileType.DIGITAL_CERTIFICATE_AUTHORIZATION_LETTER),
    
    /**
     * 担保-数字证书使用协议
     */
    GUARANTEE_DIGITAL_CERTIFICATE("13", "担保-数字证书使用协议", "guarantee-digital-certiface", FileType.DIGITAL_CERTIFICATE_USAGE_AUTHORIZATION_AGREEMENT),
    
    /**
     * 绑卡协议
     */
    SIGN_BANKCARD("14", "绑卡协议", "sign-bankcard", FileType.COLLECTION_AGREEMENT),
    
    /**
     * 担保-绑卡协议
     */
    GUARANTEE_SIGN_BANKCARD("15", "担保-绑卡协议", "guarantee-sign-bankcard", FileType.COLLECTION_AGREEMENT),
    
    /**
     * 非在校学生承诺函
     */
    NON_STUDENTS_DECLARATION("16", "非在校学生承诺函", "non-students-declaration", FileType.LETTER_OF_COMMITMENT),
    
    /**
     * 担保-非在校学生承诺函
     */
    GUARANTEE_NON_STUDENTS_DECLARATION("17", "担保-非在校学生承诺函", "guarantee-non-students-declaration", FileType.LETTER_OF_COMMITMENT),
    
    /**
     * 个人贷款承诺书
     */
    DEBET_DECLARATION("18", "个人贷款承诺书", "debet-declaration", FileType.PERSONAL_LOAN_CUSTOMER_COMMIT_CONFIRMATION),
    
    /**
     * 担保-个人贷款用途承诺书
     */
    GUARANTEE_DEBET_PURPOSE_DECLARATION("19", "担保-个人贷款用途承诺书", "guarantee-debet-purpose-declaration", FileType.PERSONAL_LOAN_USE_COMMITMENT),
    
    /**
     * 贷款告知事项及客户声明书
     */
    CUSTOMER_DECLARATION("20", "贷款告知事项及客户声明书", "customer-declaration", FileType.BORROWER_IMPORTANT_INFORMATION_TIPS),
    
    /**
     * 担保-贷款告知事项及客户声明书
     */
    GUARANTEE_CUSTOMER_DECLARATION("21", "担保-贷款告知事项及客户声明书", "guarantee-customer-declaration", FileType.BORROWER_IMPORTANT_INFORMATION_TIPS),
    
    /**
     * 个人委托担保咨询合同
     */
    PERSON_GUARANTEE_CONSULTATION("22", "个人委托担保咨询合同", "persion-guarantee-consultation", FileType.CONSULTING_SERVICE_CONTRACT),
    
    /**
     * 信用评估服务协议
     */
    CREDIT_ASSESSMENT("23", "信用评估服务协议", "credit-assessment", FileType.CONSULTING_SERVICE_CONTRACT),
    
    /**
     * 签章与存证协议
     */
    SIGNATURE_DEPOSIT("24", "签章与存证协议", "signature-deposit", FileType.DIGITAL_CERTIFICATE_AUTHORIZATION_LETTER),
    
    /**
     * 担保合同（二）
     */
    GUARANTEE_CONTRACT_2("25", "担保合同（二）", "guarantee-contract-2", FileType.DEBT_CONFIRMATION_AGREEMENT),
    
    /**
     * 担保合同（三）
     */
    GUARANTEE_CONTRACT_3("26", "担保合同（三）", "guarantee-contract-3", FileType.DEBT_CONFIRMATION_AGREEMENT),
    
    /**
     * 担保合同（四）
     */
    GUARANTEE_CONTRACT_4("27", "担保合同（四）", "guarantee-contract-4", FileType.DEBT_CONFIRMATION_AGREEMENT),
    
    /**
     * 担保合同（五）
     */
    GUARANTEE_CONTRACT_5("28", "担保合同（五）", "guarantee-contract-5", FileType.DEBT_CONFIRMATION_AGREEMENT),
    
    /**
     * 信用评估服务协议（二）
     */
    CREDIT_ASSESSMENT_2("29", "信用评估服务协议（二）", "credit-assessment-2", FileType.CONSULTING_SERVICE_CONTRACT);

    /**
     * 协议类型码
     */
    private final String code;
    
    /**
     * 协议类型名称
     */
    private final String description;
    
    /**
     * 协议标识符
     */
    private final String identifier;
    
    /**
     * 对应的FileType
     */
    private final FileType fileType;

    BairongImageType(String code, String description, String identifier, FileType fileType) {
        this.code = code;
        this.description = description;
        this.identifier = identifier;
        this.fileType = fileType;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public String getIdentifier() {
        return identifier;
    }

    public FileType getFileType() {
        return fileType;
    }

    /**
     * 根据代码获取协议类型
     * @param code 代码
     * @return 协议类型
     */
    public static BairongImageType getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (BairongImageType type : BairongImageType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据标识符获取协议类型
     * @param identifier 标识符
     * @return 协议类型
     */
    public static BairongImageType getByIdentifier(String identifier) {
        if (identifier == null) {
            return null;
        }
        for (BairongImageType type : BairongImageType.values()) {
            if (type.getIdentifier().equals(identifier)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据FileType转换为BairongImageType
     * @param fileType 文件类型
     * @return 协议类型码
     */
    public static BairongImageType fromFileType(FileType fileType) {
        if (fileType == null) {
            return null;
        }
        for (BairongImageType type : BairongImageType.values()) {
            if (fileType.equals(type.getFileType())) {
                return type;
            }
        }
        return null;
    }
}
