package com.maguo.loan.cash.flow.entrance.bairong.dto.loan;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 百融放款查询——响应数据
 * @Date 2025/9/12 14:30
 * @Version v1.0
 **/
public class BairongLoanQueryResponse {

    /**
     * 渠道放款流水号
     */
    private String outLoanSeq;
    /**
     * 资金方放款流水号
     */
    private String loanSeq;
    /**
     * 资金方授信流水号
     */
    private String applCde;
    /**
     * 放款状态
     * 100:放款中
     * 200:放款成功
     * 300:放款失败
     * 500：查无此单
     */
    private String dnSts;
    /**
     * 放款金额
     */
    private BigDecimal dnAmt;
    /**
     * 授信合同编号
     */
    private String contractNo;
    /**
     * 资金方借据号
     */
    private String loanNo;
    /**
     * 放款日期
     */
    private String loanActvDt;
    /**
     * 放款时间
     */
    private String loanActvTime;
    /**
     * 放款描述
     */
    private String payMsg;
    /**
     * 利息利率
     */
    private BigDecimal priceIntRat;
    /**
     * 对客利率
     */
    private BigDecimal custRate;

    public String getOutLoanSeq() {
        return outLoanSeq;
    }

    public void setOutLoanSeq(String outLoanSeq) {
        this.outLoanSeq = outLoanSeq;
    }

    public String getLoanSeq() {
        return loanSeq;
    }

    public void setLoanSeq(String loanSeq) {
        this.loanSeq = loanSeq;
    }

    public String getApplCde() {
        return applCde;
    }

    public void setApplCde(String applCde) {
        this.applCde = applCde;
    }

    public String getDnSts() {
        return dnSts;
    }

    public void setDnSts(String dnSts) {
        this.dnSts = dnSts;
    }

    public BigDecimal getDnAmt() {
        return dnAmt;
    }

    public void setDnAmt(BigDecimal dnAmt) {
        this.dnAmt = dnAmt;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getLoanActvDt() {
        return loanActvDt;
    }

    public void setLoanActvDt(String loanActvDt) {
        this.loanActvDt = loanActvDt;
    }

    public String getLoanActvTime() {
        return loanActvTime;
    }

    public void setLoanActvTime(String loanActvTime) {
        this.loanActvTime = loanActvTime;
    }

    public String getPayMsg() {
        return payMsg;
    }

    public void setPayMsg(String payMsg) {
        this.payMsg = payMsg;
    }

    public BigDecimal getPriceIntRat() {
        return priceIntRat;
    }

    public void setPriceIntRat(BigDecimal priceIntRat) {
        this.priceIntRat = priceIntRat;
    }

    public BigDecimal getCustRate() {
        return custRate;
    }

    public void setCustRate(BigDecimal custRate) {
        this.custRate = custRate;
    }

    public static BairongLoanQueryResponse failure(String outLoanSeq, String loanSeq, String applCde,
                                                   String dnSts, BigDecimal dnAmt, String payMsg) {
        BairongLoanQueryResponse response = new BairongLoanQueryResponse();
        response.setOutLoanSeq(outLoanSeq);
        response.setLoanSeq(loanSeq);
        response.setApplCde(applCde);
        response.setDnSts(dnSts);
        response.setDnAmt(dnAmt);
        response.setPayMsg(payMsg);
        return response;
    }
}
