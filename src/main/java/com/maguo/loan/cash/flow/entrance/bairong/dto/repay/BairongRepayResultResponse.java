package com.maguo.loan.cash.flow.entrance.bairong.dto.repay;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName BaiRongRepayResultResponse
 * <AUTHOR>
 * @Description 还款结果查询响应参数
 * @Date 2025/9/11 16:51
 * @Version v1.0
 **/
@Data
public class BairongRepayResultResponse {
    /**
     * 渠道还款流水号
     */
    @NotBlank(message = "渠道还款流水号不能为空")
    private String outBatchRepaymentSeq;

    /**
     * 资金方还款流水号
     */
    @NotBlank(message = "资金方还款流水号不能为空")
    private String batchRepaymentSeq;

    /**
     * 资金方借据号
     */
    @NotBlank(message = "资金方借据号不能为空")
    private String loanNo;

    /**
     * 还款状态
     * 取值：01-还款成功，02-还款失败，03-处理中
     */
    @NotBlank(message = "还款状态不能为空")
    private String billStatus;

    /**
     * 失败原因
     * 非必填（还款失败时必传）
     */
    private String failReason;

    /**
     * 还款金额
     */
    private BigDecimal applyRePayAmt;

    /**
     * 还款成功日期
     * 非必填（还款成功时必传，格式如yyyy-MM-dd）
     */
    private String setlValDt;

    /**
     * 还款成功时间
     * 非必填（还款成功时必传，格式如yyyy-MM-dd HH:mm:ss）
     */
    private String setlValTime;

    /**
     * 实还本金
     */
    @NotNull(message = "实还本金不能为空")
    private BigDecimal prinAmt;

    /**
     * 实还利息
     */
    @NotNull(message = "实还利息不能为空")
    private BigDecimal intAmt;

    /**
     * 实还罚息
     */
    @NotNull(message = "实还罚息不能为空")
    private BigDecimal odIntAmt;

    /**
     * 实还复利
     */
    @NotNull(message = "实还复利不能为空")
    private BigDecimal commIntAmt;

    /**
     * 实还费用
     */
    @NotNull(message = "实还费用不能为空")
    private BigDecimal feeAmt;

    /**
     * 实还融担费
     */
    @NotNull(message = "实还融担费不能为空")
    private BigDecimal grtAmt;

    /**
     * 实还违约金
     */
    @NotNull(message = "实还违约金不能为空")
    private BigDecimal penlAmt;

    /**
     * 实还其它项
     */
    @NotNull(message = "实还其它项不能为空")
    private BigDecimal otherAmt;

    /**
     * 减免金额
     */
    @NotNull(message = "减免金额不能为空")
    private BigDecimal deductAmt;

    /**
     * 判断还款是否成功
     *
     * @return true-成功（状态01），false-其他状态
     */
    public boolean isSuccess() {
        return "01".equals(billStatus);
    }

    /**
     * 判断还款是否失败
     *
     * @return true-失败（状态02），false-其他状态
     */
    public boolean isFailed() {
        return "02".equals(billStatus);
    }

    /**
     * 判断还款是否处理中
     *
     * @return true-处理中（状态03），false-其他状态
     */
    public boolean isProcessing() {
        return "03".equals(billStatus);
    }

}
