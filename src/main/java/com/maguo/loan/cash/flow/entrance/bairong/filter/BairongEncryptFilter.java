package com.maguo.loan.cash.flow.entrance.bairong.filter;

import com.alibaba.cloud.commons.io.IOUtils;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entrance.bairong.config.BairongConfig;
import com.maguo.loan.cash.flow.entrance.bairong.dto.BairongNotifyRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.BairongResponseData;
import com.maguo.loan.cash.flow.entrance.bairong.exception.BairongException;
import com.maguo.loan.cash.flow.entrance.bairong.utils.BairongEncryptDataUtils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 百融加解密过滤器
 * @date 2025/9/10 14:18
 */
public class BairongEncryptFilter extends HttpFilter {

    private static final Logger logger = LoggerFactory.getLogger(BairongEncryptFilter.class);

    /**
     * 百融系统配置信息
     * 包含AES密钥、签名密钥、渠道号码、应用ID等配置
     */
    private final BairongConfig config;

    /**
     * @param encryptFilterConfig 百融加密过滤器配置
     */
    public BairongEncryptFilter(BairongConfig encryptFilterConfig) {
        this.config = encryptFilterConfig;
    }

    /**
     * 过滤器核心处理方法
     *
     * @param req   HTTP请求对象
     * @param res   HTTP响应对象
     * @param chain 过滤器链，用于传递请求给下一个处理器
     * @throws IOException 输入输出异常
     */
    @Override
    protected void doFilter(HttpServletRequest req, HttpServletResponse res, FilterChain chain) throws IOException {
        // 1. 读取原始请求数据
        String requestStr = IOUtils.toString(req.getInputStream(), StandardCharsets.UTF_8);
        logger.info("百融 入参原始报文: {}, 是否跳过验签: {}, URL: {}", requestStr, config.isSkipSignVerify(), req.getRequestURL());

        try {
            // 2. 解析加密的请求数据结构
            BairongNotifyRequest bairongRequestData = JsonUtil.convertToObject(requestStr, BairongNotifyRequest.class);
            String decryptedJson;

            // 3. 根据配置和渠道判断是否需要验签
            if (config.isSkipSignVerify() || "test".equals(bairongRequestData.getChannel())) {
                // 测试模式或跳过验签配置：直接解密JSON数据
                decryptedJson = BairongEncryptDataUtils.decryptJson(bairongRequestData.getJson(), config.getAesKey());
            } else {
                // 正常模式：先验证签名，验证通过后再解密
                boolean signResult = BairongEncryptDataUtils.checkSignAndDecrypt(bairongRequestData, config.getAesKey(), config.getSignKey());
                if (!signResult) {
                    logger.error("百融验签失败, 原始报文: {}", requestStr);
                    throw BairongException.SIGN_VERIFY_FAIL;
                }

                logger.info("百融 入参验签成功: {}", JsonUtil.toJsonString(bairongRequestData));
                decryptedJson = BairongEncryptDataUtils.decryptJson(bairongRequestData.getJson(), config.getAesKey());
            }

            // 4. 将解密后的数据设置为请求属性，供后续业务使用
            req.setAttribute("decryptedRequestBody", decryptedJson);
            logger.info("百融 入参解密后业务数据: {}", decryptedJson);

            // 5. 创建请求包装器，替换原始请求体为解密后的数据
            ReplaceInputHttpRequestWrapper requestWrapper = new ReplaceInputHttpRequestWrapper(req, decryptedJson.getBytes(StandardCharsets.UTF_8));
            // 创建响应包装器，用于缓存响应内容
            ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(res);

            // 6. 继续执行过滤器链，让业务逻辑处理解密后的数据
            chain.doFilter(requestWrapper, responseWrapper);
            //传入资方id不为空则返回明文无加密加签
            boolean flag = bairongRequestData.getFundId() != null;
            // 7. 处理业务处理完成后的响应数据
            processResponse(req, res, responseWrapper, flag);

        } catch (Exception e) {
            logger.error("百融 调用异常", e);
            handleException(res, e);
        }
    }

    /**
     * 处理业务响应数据
     *
     * @param req             原始HTTP请求对象
     * @param res             HTTP响应对象
     * @param responseWrapper 响应包装器，包含业务处理结果
     * @throws IOException 输入输出异常
     */
    private void processResponse(HttpServletRequest req, HttpServletResponse res, ContentCachingResponseWrapper responseWrapper, boolean flag) throws IOException {
        // 设置响应字符编码和内容类型
        res.setCharacterEncoding("utf-8");
        res.setContentType("application/json");

        // 获取业务处理后的响应内容
        byte[] contentBytes = responseWrapper.getContentAsByteArray();
        String responseStr = new String(contentBytes, StandardCharsets.UTF_8);
        logger.info("百融 出参原始报文: {}, URL: {}", responseStr, req.getRequestURL());
        if (flag) {
            //无需加密加签直接返回
            writeResponse(res, responseStr);
            return;
        }
        // 构建符合百融规范的加密响应数据结构
        BairongResponseData encryptedResponse = new BairongResponseData();
        encryptedResponse.setChannel(config.getChannelCode());
        encryptedResponse.setAppId(config.getAppId());
        encryptedResponse.setJson(responseStr);
        // 设置随机字符串为当前时间，符合项目规范（yyyy-MM-dd HH:mm:ss格式）
        encryptedResponse.setRandomStr(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        // 根据配置决定是否需要加密和签名
        if (!config.isSkipSignVerify()) {
            // 对响应数据进行AES加密和签名处理
            BairongEncryptDataUtils.signAndEncrypt(encryptedResponse, config.getAesKey(), config.getSignKey());
        }

        // 将加密后的响应对象转换为JSON字符串
        String jsonString = JsonUtil.toJsonString(encryptedResponse);
        logger.info("百融 出参加密后报文: {}, URL: {}", jsonString, req.getRequestURL());

        // 写入HTTP响应流
        writeResponse(res, jsonString);
    }

    /**
     * 将响应数据写入HTTP响应流
     *
     * @param res             HTTP响应对象
     * @param responseContent 响应内容
     * @throws IOException 输入输出异常
     */
    private void writeResponse(HttpServletResponse res, String responseContent) throws IOException {
        byte[] contentBytes = responseContent.getBytes(StandardCharsets.UTF_8);
        res.setContentLength(contentBytes.length);

        ServletOutputStream outputStream = res.getOutputStream();
        outputStream.write(contentBytes);
        outputStream.flush();
    }

    /**
     * 处理异常响应
     *
     * @param res HTTP响应对象
     * @param e   发生的异常
     * @throws IOException 输入输出异常
     */
    private void handleException(HttpServletResponse res, Exception e) throws IOException {
        // 设置响应字符编码和内容类型
        res.setCharacterEncoding("utf-8");
        res.setContentType("application/json");

        // 根据异常类型构建错误信息
        String errorMsg = e.getMessage();
        if (e instanceof BairongException) {
            BairongException bizEx = (BairongException) e;
            // 业务异常：包含错误码和详细错误信息
            errorMsg = "错误码: " + bizEx.getErrorCode() + ", 错误信息: " + bizEx.getMessage();
        }

        // 构建异常响应数据，使用静态工厂方法创建
        BairongResponseData errorResponse = BairongResponseData.exception(errorMsg, config.getChannelCode(), config.getAppId());
        String errorJson = JsonUtil.toJsonString(errorResponse);

        // 写入错误响应
        writeResponse(res, errorJson);
    }
}
