package com.maguo.loan.cash.flow.entrance.bairong.controller;

import com.maguo.loan.cash.flow.entrance.bairong.dto.agreement.BairongAgreementQueryRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.agreement.BairongAgreementQueryResponse;
import com.maguo.loan.cash.flow.entrance.bairong.service.BairongAgreementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 百融协议相关
 * @date 2025/9/11 15:53
 */
@RestController
@RequestMapping("/image/fetch")
public class BairongAgreementController extends BairongApiValidator {
    @Autowired
    private BairongAgreementService bairongAgreementService;

    /**
     * 获取协议地址
     *
     * @param request
     * @return
     */
    @RequestMapping("/BRYC")
    public BairongAgreementQueryResponse fetchAgreementUrl(@RequestBody BairongAgreementQueryRequest request) {
        return bairongAgreementService.fetchAgreementUrl(request);
    }
}
