package com.maguo.loan.cash.flow.entrance.bairong.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/9/12 11:21
 */
public enum BairongContractType {
    /**
     * 人行征信查询授权书
     */
    CREDIT_PBC_PROTOCOL(1, "人行征信查询授权书", "credit-pbc-protocol"),

    /**
     * 担保-人行征信查询授权书
     */
    GUARANTEE_CREDIT_PBC_PROTOCOL(2, "担保-人行征信查询授权书", "guarantee-credit-pbc-protocol"),

    /**
     * 个人信息授权书
     */
    CREDIT_PERSON_PROTOCOL(3, "个人信息授权书", "credit-person-protocol"),

    /**
     * 担保-个人信息授权书
     */
    GUARANTEE_CREDIT_PERSON_PROTOCOL(4, "担保-个人信息授权书", "guarantee-credit-person-protocol"),

    /**
     * 个人借款合同
     */
    BORROW_CONTRACT(5, "个人借款合同", "borrow-contract"),

    /**
     * 担保合同
     */
    GUARANTEE_CONTRACT(6, "担保合同", "guarantee-contract"),

    /**
     * 个人扣款授权协议
     */
    WITHDRAW_PROTOCOL(7, "个人扣款授权协议", "withdraw-protocal"),

    /**
     * 担保-个人扣款授权协议
     */
    GUARANTEE_WITHDRAW_PROTOCOL(8, "担保-个人扣款授权协议", "guarantee-withdraw-protocal"),

    /**
     * 综合授权书
     */
    CREDIT_MIX_PROTOCOL(9, "综合授权书", "credit-mix-protocol"),

    /**
     * 担保-综合授权书
     */
    GUARANTEE_CREDIT_MIX_PROTOCOL(10, "担保-综合授权书", "guarantee-credit-mix-protocol"),

    /**
     * 授信额度协议
     */
    CREDIT_CONTRACT(11, "授信额度协议", "credit-contract"),

    /**
     * 数字证书使用协议
     */
    DIGITAL_CERTIFICATE(12, "数字证书使用协议", "digital-certiface"),

    /**
     * 担保-数字证书使用协议
     */
    GUARANTEE_DIGITAL_CERTIFICATE(13, "担保-数字证书使用协议", "guarantee-digital-certiface"),

    /**
     * 绑卡协议
     */
    SIGN_BANKCARD(14, "绑卡协议", "sign-bankcard"),

    /**
     * 担保-绑卡协议
     */
    GUARANTEE_SIGN_BANKCARD(15, "担保-绑卡协议", "guarantee-sign-bankcard"),

    /**
     * 非在校学生承诺函
     */
    NON_STUDENTS_DECLARATION(16, "非在校学生承诺函", "non-students-declaration"),

    /**
     * 担保-非在校学生承诺函
     */
    GUARANTEE_NON_STUDENTS_DECLARATION(17, "担保-非在校学生承诺函", "guarantee-non-students-declaration"),

    /**
     * 个人贷款承诺书
     */
    DEBET_DECLARATION(18, "个人贷款承诺书", "debet-declaration"),

    /**
     * 担保-个人贷款用途承诺书
     */
    GUARANTEE_DEBET_PURPOSE_DECLARATION(19, "担保-个人贷款用途承诺书", "guarantee-debet-purpose-declaration"),

    /**
     * 贷款告知事项及客户声明书
     */
    CUSTOMER_DECLARATION(20, "贷款告知事项及客户声明书", "customer-declaration"),

    /**
     * 担保-贷款告知事项及客户声明书
     */
    GUARANTEE_CUSTOMER_DECLARATION(21, "担保-贷款告知事项及客户声明书", "guarantee-customer-declaration"),

    /**
     * 个人委托担保咨询合同
     */
    PERSON_GUARANTEE_CONSULTATION(22, "个人委托担保咨询合同", "persion-guarantee-consultation"),

    /**
     * 信用评估服务协议
     */
    CREDIT_ASSESSMENT(23, "信用评估服务协议", "credit-assessment"),

    /**
     * 签章与存证协议
     */
    SIGNATURE_DEPOSIT(24, "签章与存证协议", "signature-deposit"),

    /**
     * 担保合同（二）
     */
    GUARANTEE_CONTRACT_2(25, "担保合同（二）", "guarantee-contract-2"),

    /**
     * 担保合同（三）
     */
    GUARANTEE_CONTRACT_3(26, "担保合同（三）", "guarantee-contract-3"),

    /**
     * 担保合同（四）
     */
    GUARANTEE_CONTRACT_4(27, "担保合同（四）", "guarantee-contract-4"),

    /**
     * 担保合同（五）
     */
    GUARANTEE_CONTRACT_5(28, "担保合同（五）", "guarantee-contract-5"),

    /**
     * 信用评估服务协议（二）
     */
    CREDIT_ASSESSMENT_2(29, "信用评估服务协议（二）", "credit-assessment-2");

    /**
     * 合同类型编码
     */
    private final Integer code;

    /**
     * 合同类型名称
     */
    private final String name;

    /**
     * 合同类型标识
     */
    private final String identifier;

    BairongContractType(Integer code, String name, String identifier) {
        this.code = code;
        this.name = name;
        this.identifier = identifier;
    }

    /**
     * 根据编码获取合同类型
     *
     * @param code 编码
     * @return 合同类型
     */
    public static BairongContractType getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (BairongContractType type : BairongContractType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据标识获取合同类型
     *
     * @param identifier 标识
     * @return 合同类型
     */
    public static BairongContractType getByIdentifier(String identifier) {
        if (identifier == null || identifier.trim().isEmpty()) {
            return null;
        }
        for (BairongContractType type : BairongContractType.values()) {
            if (type.getIdentifier().equals(identifier)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据名称获取合同类型
     *
     * @param name 名称
     * @return 合同类型
     */
    public static BairongContractType getByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }
        for (BairongContractType type : BairongContractType.values()) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getIdentifier() {
        return identifier;
    }
}
