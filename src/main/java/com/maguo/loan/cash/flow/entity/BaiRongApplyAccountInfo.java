package com.maguo.loan.cash.flow.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

@Entity
@Table(name = "bairong_apply_account_info")
public class BaiRongApplyAccountInfo extends BaseEntity {

    /**
     * 账号类别
     */
    @Column(name = "acct_kind")
    private String acctKind;

    /**
     * 账户开户行编码
     */
    @Column(name = "acct_bank_code")
    private String acctBankCode;

    /**
     * 账号
     */
    @Column(name = "acct_no")
    private String acctNo;

    /**
     * 账号开户名
     */
    @Column(name = "acct_name")
    private String acctName;

    /**
     * 开户人证件类型
     */
    @Column(name = "id_typ")
    private String idTyp;

    /**
     * 开户人证件号码
     */
    @Column(name = "id_no")
    private String idNo;

    /**
     * 开户行预留电话
     */
    @Column(name = "acct_phone")
    private String acctPhone;

    /**
     * 支付通道 TL-通联 YB-易宝
     */
    @Column(name = "pay_channel")
    private String payChannel;

    /**
     * 协议号，“账号类别”为02时必传
     */
    @Column(name = "agree_num")
    private String agreeNum;


    public String getAcctKind() {
        return acctKind;
    }

    public void setAcctKind(String acctKind) {
        this.acctKind = acctKind;
    }

    public String getAcctBankCode() {
        return acctBankCode;
    }

    public void setAcctBankCode(String acctBankCode) {
        this.acctBankCode = acctBankCode;
    }

    public String getAcctNo() {
        return acctNo;
    }

    public void setAcctNo(String acctNo) {
        this.acctNo = acctNo;
    }

    public String getAcctName() {
        return acctName;
    }

    public void setAcctName(String acctName) {
        this.acctName = acctName;
    }

    public String getIdTyp() {
        return idTyp;
    }

    public void setIdTyp(String idTyp) {
        this.idTyp = idTyp;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getAcctPhone() {
        return acctPhone;
    }

    public void setAcctPhone(String acctPhone) {
        this.acctPhone = acctPhone;
    }

    public String getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }

    public String getAgreeNum() {
        return agreeNum;
    }

    public void setAgreeNum(String agreeNum) {
        this.agreeNum = agreeNum;
    }
}

