package com.maguo.loan.cash.flow.entrance.bairong.service;

import com.maguo.loan.cash.flow.entity.bairong.BairongAgreementUploadRecord;
import com.maguo.loan.cash.flow.entrance.bairong.dto.agreement.BairongAgreementQueryRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.agreement.BairongAgreementQueryResponse;
import com.maguo.loan.cash.flow.entrance.bairong.dto.agreement.BairongAgreementQueryResponse.ContractInfo;
import com.maguo.loan.cash.flow.entrance.bairong.exception.BairongException;
import com.maguo.loan.cash.flow.entrance.bairong.exception.BairongResultCode;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.repository.bairong.BairongAgreementUploadRecordRepository;
import com.maguo.loan.cash.flow.service.FileService;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/9/12 13:59
 */
@Service
public class BairongAgreementService {

    private static final Logger logger = LoggerFactory.getLogger(BairongAgreementService.class);


    @Autowired
    private BairongAgreementUploadRecordRepository bairongAgreementUploadRecordRepository;

    @Autowired
    private FileService fileService;


    /**
     * 查询百融协议文件
     *
     * @param request 查询请求
     * @return 查询响应
     */
    public BairongAgreementQueryResponse fetchAgreementUrl(BairongAgreementQueryRequest request) {
        logger.info("开始查询百融协议文件, request: {}", request);

        // 验证请求参数
        BairongResultCode validationError = request.validate();
        if (validationError != null) {
            throw new BairongException(validationError);
        }
        List<BairongAgreementUploadRecord> records = bairongAgreementUploadRecordRepository
            .findByConditions(request.getOutApplSeq(), request.getOutLoanSeq(),
                request.getBusinessStage(), ProcessState.SUCCEED);


        BairongAgreementQueryResponse response = new BairongAgreementQueryResponse();

        if (CollectionUtils.isEmpty(records)) {
            logger.info("未找到符合条件的协议文件");
            response.setContractList(new ArrayList<>());
            return response;
        }

        logger.info("找到 {} 个符合条件的协议文件", records.size());

        // 转换为响应格式
        List<ContractInfo> contractList = new ArrayList<>();
        for (BairongAgreementUploadRecord record : records) {
            try {
                BairongAgreementQueryResponse.ContractInfo contractInfo =
                    new BairongAgreementQueryResponse.ContractInfo();
                // 下载文件内容
                byte[] fileData = downloadFileFromOss(record.getOssBucket(), record.getOssKey());
                contractInfo.setData(fileData);
                contractInfo.setFileName(record.getFileName());
                contractInfo.setImageType(record.getImageType());

                contractList.add(contractInfo);
                logger.info("成功处理协议文件: {}", record.getFileName());
            } catch (Exception e) {
                logger.error("处理协议文件失败, recordId: {}, fileName: {}",
                    record.getId(), record.getFileName(), e);
                // 继续处理其他文件，不因单个文件失败而中断
            }
        }

        response.setContractList(contractList);
        logger.info("查询百融协议文件完成, 返回 {} 个文件", contractList.size());
        return response;
    }

    /**
     * 从OSS下载文件内容
     *
     * @param bucket OSS桶名
     * @param key    OSS文件key
     * @return 文件字节数组
     */
    private byte[] downloadFileFromOss(String bucket, String key) {
        if (StringUtils.isAnyBlank(bucket, key)) {
            throw new IllegalArgumentException("OSS bucket或key为空");
        }

        try {
            return fileService.downloadFileBytes(bucket, key);
        } catch (Exception e) {
            logger.error("从OSS下载文件失败, bucket: {}, key: {}", bucket, key, e);
            throw new RuntimeException("下载文件失败: " + e.getMessage(), e);
        }
    }


}
