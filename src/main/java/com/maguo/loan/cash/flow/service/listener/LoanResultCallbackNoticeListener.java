package com.maguo.loan.cash.flow.service.listener;

import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.entrance.bairong.service.BairongCallbackService;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 放款结果回调通知
 */
@Component
public class LoanResultCallbackNoticeListener extends AbstractListener {

    private static final Logger logger = LoggerFactory.getLogger(LoanResultCallbackNoticeListener.class);

    public LoanResultCallbackNoticeListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private BairongCallbackService bairongCallbackService;

    @RabbitListener(queues = RabbitConfig.Queues.BR_LOAN_CALLBACK_NOTICE)
    public void loanResultCallbackNoticeListen(Message message, Channel channel) {
        String orderId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("监听放款结果回调通知百融方放款结果信息数据:{}", orderId);
            // service
            bairongCallbackService.bairongLoanResultCallbackNotice(orderId);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            getMqWarningService().warn("放款结果回调通知异常:orderId," + orderId + "," + e.getMessage(), msg -> logger.error(msg, e));
        } finally {
            ackMsg(orderId, message, channel);
        }
    }

}
