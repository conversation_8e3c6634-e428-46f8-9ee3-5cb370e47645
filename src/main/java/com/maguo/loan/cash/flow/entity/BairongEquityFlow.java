package com.maguo.loan.cash.flow.entity;

import com.maguo.loan.cash.flow.enums.WhetherState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.time.LocalDateTime;

/**
 * 百融权益流水信息表
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "bairong_equity_flow")
public class BairongEquityFlow extends BaseEntity {

    @Override
    protected String prefix() {
        return "BRE";
    }

    /**
     * 借据号
     */
    private String loanId;
    /**
     * 渠道放款流水号
     */
    private String outLoanSeq;
    /**
     * 权益订单号
     */
    private String rightsOrderNo;
    /**
     * 是否获取 Y：是  N：否  默认是N
     */
    @Enumerated(EnumType.STRING)
    private WhetherState isObtain;
    /**
     * 拉取次数 默认为0
     */
    private Integer pullFrequency;
    /**
     * 放款成功时间
     */
    private LocalDateTime loanTime;

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getOutLoanSeq() {
        return outLoanSeq;
    }

    public void setOutLoanSeq(String outLoanSeq) {
        this.outLoanSeq = outLoanSeq;
    }

    public String getRightsOrderNo() {
        return rightsOrderNo;
    }

    public void setRightsOrderNo(String rightsOrderNo) {
        this.rightsOrderNo = rightsOrderNo;
    }

    public WhetherState getIsObtain() {
        return isObtain;
    }

    public void setIsObtain(WhetherState isObtain) {
        this.isObtain = isObtain;
    }

    public Integer getPullFrequency() {
        return pullFrequency;
    }

    public void setPullFrequency(Integer pullFrequency) {
        this.pullFrequency = pullFrequency;
    }

    public LocalDateTime getLoanTime() {
        return loanTime;
    }

    public void setLoanTime(LocalDateTime loanTime) {
        this.loanTime = loanTime;
    }
}
