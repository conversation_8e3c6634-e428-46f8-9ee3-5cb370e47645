package com.maguo.loan.cash.flow.entrance.bairong.service;

import com.jinghang.common.util.DateUtil;
import com.maguo.loan.cash.flow.entity.BaiRongApplyRecord;
import com.maguo.loan.cash.flow.entrance.bairong.dto.BairongImageUploadRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.BairongImageUploadResponse;
import com.maguo.loan.cash.flow.entrance.bairong.enums.BairongImageInfoEnum;
import com.maguo.loan.cash.flow.entrance.bairong.exception.BairongException;
import com.maguo.loan.cash.flow.entrance.bairong.exception.BairongResultCode;
import com.maguo.loan.cash.flow.repository.bairong.BairongApplyRecordRepository;
import com.maguo.loan.cash.flow.service.FileService;
import groovy.util.logging.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@Service
public class BairongImageUploadService {

    private static final Logger logger = LoggerFactory.getLogger(BairongImageUploadService.class);
    @Value(value = "${oss.bucket.name}")
    private String ossBucket;
    @Autowired
    private FileService fileService;
    @Autowired
    private BairongApplyRecordRepository bairongApplyRecordRepository;

    /**
     * 执行影像上传
     *
     * @param request 上传请求
     * @return 上传结果
     */
    public BairongImageUploadResponse uploadImage(BairongImageUploadRequest request) {
        logger.info("开始执行百融影像上传，渠道流水号: {}, 影像类型: {}",
            request.getOutApplSeq(), request.getImgType());

        // 参数校验
        validateRequest(request);

        // 处理文件上传逻辑
        BairongImageUploadResponse response = processFileUpload(request);

        logger.info("百融影像上传完成，渠道流水号: {}, 影像ID: {}",
            request.getOutApplSeq(), response.getImageId());
        return response;
    }

    /**
     * 参数校验
     */
    private void validateRequest(BairongImageUploadRequest request) {
        logger.info("开始参数校验，请求参数: {}", request);
        if (Objects.isNull(request)) {
            logger.error("请求参数不能为空");
            throw new BairongException(BairongResultCode.INVALID_PARAM);
        }
        if (Objects.isNull(request.getOutApplSeq()) || request.getOutApplSeq().trim().isEmpty()) {
            logger.error("渠道流水号不能为空");
            throw new BairongException(BairongResultCode.INVALID_PARAM);
        }
        if (Objects.isNull(request.getImgType()) || request.getImgType().trim().isEmpty()) {
            logger.error("影像类型不能为空");
            throw new BairongException(BairongResultCode.INVALID_PARAM);
        }
        if (Objects.isNull(request.getApplyTime()) || request.getApplyTime().trim().isEmpty()) {
            logger.error("申请时间不能为空");
            throw new BairongException(BairongResultCode.INVALID_PARAM);
        }
        if (Objects.isNull(request.getFileName()) || request.getFileName().trim().isEmpty()) {
            logger.error("影像名称不能为空");
            throw new BairongException(BairongResultCode.INVALID_PARAM);
        }
        if (Objects.isNull(request.getFileBytes()) || request.getFileBytes().length == 0) {
            logger.error("文件字节流不能为空");
            throw new BairongException(BairongResultCode.INVALID_PARAM);
        }

        logger.info("参数校验通过");
    }

    /**
     * 处理文件上传
     */
    private BairongImageUploadResponse processFileUpload(BairongImageUploadRequest request) {
        logger.info("开始处理文件上传，渠道流水号: {}, 影像类型: {}",
            request.getOutApplSeq(), request.getImgType());

        BairongImageUploadResponse bairongUploadResponse = new BairongImageUploadResponse();

        // 先根据渠道流水号查询记录
        Optional<BaiRongApplyRecord> recordOpt = bairongApplyRecordRepository.findByOrderNo(request.getOutApplSeq());
        BaiRongApplyRecord bairongApplyRecord;
        String imageId;

        if (recordOpt.isPresent()) {
            // 如果有记录，只更新图片字段
            bairongApplyRecord = recordOpt.get();
            imageId = bairongApplyRecord.getImageId();
            logger.info("找到已有记录，只更新图片字段，渠道流水号: {}", request.getOutApplSeq());
        } else {
            // 如果没有记录，创建新记录并设置基础字段
            bairongApplyRecord = new BaiRongApplyRecord();
            imageId = generateImageId();
            bairongApplyRecord.setImageId(imageId);
            bairongApplyRecord.setOrderNo(request.getOutApplSeq());
            logger.info("未找到记录，创建新记录，渠道流水号: {}", request.getOutApplSeq());
        }

        // 获取图片字节流
        byte[] fileBytes = request.getFileBytes();
        // 将图片字节流转换为输入流
        InputStream inputStream = new ByteArrayInputStream(fileBytes);

        // 根据图片类型处理上传和保存
        try {
            if (request.getImgType().equals(BairongImageInfoEnum.ID_CARD_FRONT.getCode())) {
                // 身份证头像面
                String headOssKey = uploadToOss(inputStream, ossBucket, "head", request.getOutApplSeq(), "jpg");
                bairongApplyRecord.setIdPositive(headOssKey);
                bairongApplyRecordRepository.save(bairongApplyRecord);
            } else if (request.getImgType().equals(BairongImageInfoEnum.ID_CARD_BACK.getCode())) {
                // 身份证国徽面
                String headOssKey = uploadToOss(inputStream, ossBucket, "nation", request.getOutApplSeq(), "jpg");
                bairongApplyRecord.setIdNegative(headOssKey);
                bairongApplyRecordRepository.save(bairongApplyRecord);
            } else if (request.getImgType().equals(BairongImageInfoEnum.FACE_RECOGNITION.getCode())) {
                // 人脸识别照
                String headOssKey = uploadToOss(inputStream, ossBucket, "livePhoto", request.getOutApplSeq(), "jpg");
                bairongApplyRecord.setLivePhoto(headOssKey);
                bairongApplyRecordRepository.save(bairongApplyRecord);
            } else {
                logger.error("不支持的图片类型: {}, 渠道流水号: {}", request.getImgType(), request.getOutApplSeq());
                throw new BairongException(BairongResultCode.INVALID_PARAM);
            }
        } catch (Exception e) {
            logger.error("图片上传或保存失败，渠道流水号: {}, 影像类型: {}", request.getOutApplSeq(), request.getImgType(), e);
            throw new BairongException(BairongResultCode.IMAGE_UPLOAD_FAIL);
        }

        // 返回imageId 影像Id
        bairongUploadResponse.setImageId(imageId);
        logger.info("文件上传处理完成，返回影像ID: {}", imageId);
        return bairongUploadResponse;
    }

    private String uploadToOss(InputStream inputStream, String bucket, String picType, String openId, String imageType) {
        String picKey = generateOssPicKey(openId, picType, imageType);
        try {
            fileService.uploadOss(bucket, picKey, inputStream);
        } catch (Exception e) {
            logger.error("bairong info process pic error, openId: {}, fileType: {}", openId, picType, e);
            throw new BairongException(BairongResultCode.OSS_UPLOAD_FAIL);
        }
        return picKey;
    }

    private String generateOssPicKey(String openId, String prefix, String imageType) {
        String dayStr = DateUtil.formatShort(new Date());
        return "bairong/info/" + dayStr + "/" + openId + "/" + prefix + "_"
            + UUID.randomUUID().toString().replaceAll("-", "") + "." + imageType;
    }

    /**
     * 生成符合规则的imageId
     * 规则：BRIM+年月日时分秒+盐值
     * 保证大量数据下的唯一性
     *
     * @return imageId
     */
    private String generateImageId() {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 格式化为年月日时分秒
        String dateTimeStr = now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        // 生成纳秒部分作为额外唯一性保障
        String nanoTime = String.valueOf(System.nanoTime() % 1000000);
        // 生成UUID的一部分作为盐值
        String uuidPart = UUID.randomUUID().toString().replace("-", "").substring(0, 6);
        // 拼接imageId (BRIM + 年月日时分秒 + 纳秒部分 + UUID部分)
        return "BRIM" + dateTimeStr + nanoTime + uuidPart;
    }
}
