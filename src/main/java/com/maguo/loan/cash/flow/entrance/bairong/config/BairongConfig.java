package com.maguo.loan.cash.flow.entrance.bairong.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 百融配置类
 * @date 2025/9/10 14:44
 */
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "bairong.config")
public class BairongConfig {

    // getter and setter methods
    /**
     * 是否跳过签名验证
     */
    private boolean skipSignVerify = false;

    /**
     * AES密钥（jsonKey）
     */
    private String aesKey;

    /**
     * 渠道号码
     */
    private String channelCode;

    /**
     * 接入方系统ID
     */
    private String appId;

    /**
     * 签名密钥
     */
    private String signKey;

    // SFTP配置
    private String sftpUser;
    private String sftpPassword;
    private String sftpHost;
    private Integer sftpPort;
    // /upload/{资金方简称}/{产品编码}/in/files
    private String sftpReconPath;

    /**
     * 百融回调接口地址
     */
    private String callbackUrl;

    private String sftpDownloadPath;
}
