package com.maguo.loan.cash.flow.service.listener;

import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.entrance.bairong.service.BairongCallbackService;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 还款结果回调通知
 */
@Component
public class RepayCallbackNoticeListener extends AbstractListener {

    private static final Logger logger = LoggerFactory.getLogger(RepayCallbackNoticeListener.class);

    public RepayCallbackNoticeListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private BairongCallbackService bairongCallbackService;

    @RabbitListener(queues = RabbitConfig.Queues.BR_REPAY_CALLBACK_NOTICE)
    public void loanResultCallbackNoticeListen(Message message, Channel channel) {
        String loanId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("监听还款结果回调通知百融方放款结果信息数据:{}", loanId);
            // service
            bairongCallbackService.bairongRepayResultCallbackNotice(loanId);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            getMqWarningService().warn("还款结果回调通知异常:orderId," + loanId + "," + e.getMessage(), msg -> logger.error(msg, e));
        } finally {
            ackMsg(loanId, message, channel);
        }
    }

}
