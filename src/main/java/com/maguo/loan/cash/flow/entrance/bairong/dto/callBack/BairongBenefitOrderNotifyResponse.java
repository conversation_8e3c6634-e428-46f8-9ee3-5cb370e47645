package com.maguo.loan.cash.flow.entrance.bairong.dto.callBack;

import lombok.Data;

/**
 * 权益订单通知 - 响应参数实体类
 * = * 功能描述：向百融返回权益订单通知的接收结果
 */
@Data
public class BairongBenefitOrderNotifyResponse {

    /**
     * 说明：标识通知接收结果，可选值：
     * 01 - 接收成功
     * 02 - 接收失败
     */
    private String resultCode;

    /**
     * 说明：对结果码的补充说明，如接收失败时可填写失败原因（例：“参数格式错误”“系统暂不可用”）
     */
    private String resultDesc;
}
