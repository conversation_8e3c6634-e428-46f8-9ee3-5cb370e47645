package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.BoundSide;
import com.maguo.loan.cash.flow.enums.ProcessState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

/**
 * <AUTHOR> gale
 * @Description 百融换绑记录表
 * @Date 2024/2/19 16:22
 */
@Entity
@Table(name = "bairong_rebind_record")
public class BairongRebindRecord extends BaseEntity {

    /**
     * 绑卡状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessState state;
    /**
     * 授信流水号
     */
    private String creditId;
    /**
     * 用户id
     */
    private String userId;

    /**
     * 阶段
     */
    private String loanStage;

    /**
     * 绑卡记录id
     */
    private String bindCardRecordId;

    @Enumerated(EnumType.STRING)
    private BoundSide boundSide;

    public ProcessState getState() {
        return state;
    }

    public void setState(ProcessState state) {
        this.state = state;
    }

    public String getCreditId() {
        return creditId;
    }

    public void setCreditId(String creditId) {
        this.creditId = creditId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getLoanStage() {
        return loanStage;
    }

    public void setLoanStage(String loanStage) {
        this.loanStage = loanStage;
    }

    public String getBindCardRecordId() {
        return bindCardRecordId;
    }

    public void setBindCardRecordId(String bindCardRecordId) {
        this.bindCardRecordId = bindCardRecordId;
    }

    public BoundSide getBoundSide() {
        return boundSide;
    }

    public void setBoundSide(BoundSide boundSide) {
        this.boundSide = boundSide;
    }

}
