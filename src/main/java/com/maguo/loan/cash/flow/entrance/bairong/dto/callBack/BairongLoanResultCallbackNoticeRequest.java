package com.maguo.loan.cash.flow.entrance.bairong.dto.callBack;

import java.math.BigDecimal;

/**
 * 百融放款结果回调结果通知接口请求参数
 *
 * <AUTHOR>
 * @Date 2025-09-15
 */
public class BairongLoanResultCallbackNoticeRequest {

    /**
     * 渠道授信流水号
     */
    private String outApplSeq;
    /**
     * 渠道放款流水号
     */
    private String outLoanSeq;
    /**
     * 资金方授信流水号
     */
    private String applCde;
    /**
     * 资金方放款流水号
     */
    private String loanSeq;
    /**
     * 授信合同编号
     */
    private String contractNo;
    /**
     * 资金方借据号
     * 当放款状态为200时必录
     */
    private String loanNo;
    /**
     * 放款状态
     * 100:放款中
     * 200:放款成功
     * 300:放款失败
     * 500:查无此单
     */
    private String dnSts;
    /**
     * 放款金额
     */
    private BigDecimal dnAmt;
    /**
     * 放款日期
     * 格式:yyyy-MM-dd
     */
    private String loanActvDt;
    /**
     * 放款时间
     * 格式:yyyy-MM-dd hh:mm:ss
     */
    private String loanActvTime;
    /**
     * 放款描述
     * 放款失败原因，放款失败必传
     */
    private String payMsg;
    /**
     * 利息利率
     * 资金方出资年化利率，小数形式，放款成功必传
     */
    private BigDecimal priceIntRat;
    /**
     * 对客利率
     * 对客综合年化利率，小数形式
     */
    private BigDecimal custRate;

    public String getOutApplSeq() {
        return outApplSeq;
    }

    public void setOutApplSeq(String outApplSeq) {
        this.outApplSeq = outApplSeq;
    }

    public String getOutLoanSeq() {
        return outLoanSeq;
    }

    public void setOutLoanSeq(String outLoanSeq) {
        this.outLoanSeq = outLoanSeq;
    }

    public String getApplCde() {
        return applCde;
    }

    public void setApplCde(String applCde) {
        this.applCde = applCde;
    }

    public String getLoanSeq() {
        return loanSeq;
    }

    public void setLoanSeq(String loanSeq) {
        this.loanSeq = loanSeq;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getDnSts() {
        return dnSts;
    }

    public void setDnSts(String dnSts) {
        this.dnSts = dnSts;
    }

    public BigDecimal getDnAmt() {
        return dnAmt;
    }

    public void setDnAmt(BigDecimal dnAmt) {
        this.dnAmt = dnAmt;
    }

    public String getLoanActvDt() {
        return loanActvDt;
    }

    public void setLoanActvDt(String loanActvDt) {
        this.loanActvDt = loanActvDt;
    }

    public String getLoanActvTime() {
        return loanActvTime;
    }

    public void setLoanActvTime(String loanActvTime) {
        this.loanActvTime = loanActvTime;
    }

    public String getPayMsg() {
        return payMsg;
    }

    public void setPayMsg(String payMsg) {
        this.payMsg = payMsg;
    }

    public BigDecimal getPriceIntRat() {
        return priceIntRat;
    }

    public void setPriceIntRat(BigDecimal priceIntRat) {
        this.priceIntRat = priceIntRat;
    }

    public BigDecimal getCustRate() {
        return custRate;
    }

    public void setCustRate(BigDecimal custRate) {
        this.custRate = custRate;
    }
}
