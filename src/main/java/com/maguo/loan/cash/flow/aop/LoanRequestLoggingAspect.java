package com.maguo.loan.cash.flow.aop;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maguo.loan.cash.flow.annotation.LogLoanRequest;
import com.maguo.loan.cash.flow.entity.LoanApplication;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.common.ProjectProductMapping;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.repository.LoanApplicationRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.ProjectProductMappingRepository;
import com.maguo.loan.cash.flow.util.AgeUtil;
import com.maguo.loan.cash.flow.util.GioPushUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Aspect
@Component
public class LoanRequestLoggingAspect {
    private static final Logger logger = LoggerFactory.getLogger(LoanRequestLoggingAspect.class);
    private final LoanApplicationRepository loanApplicationRepository;
    private final OrderRepository orderRepository;
    private final ObjectMapper objectMapper;
    private static final ThreadLocal<String> LOAN_APPLICATION_LOG_ID = new ThreadLocal<>();
    private static final int MAX_BYTE_VALUE = 255;
    private final ProjectProductMappingRepository projectProductMappingRepository;

    public LoanRequestLoggingAspect(LoanApplicationRepository loanApplicationRepository, OrderRepository orderRepository, ObjectMapper objectMapper, ProjectProductMappingRepository projectProductMappingRepository) {
        this.loanApplicationRepository = loanApplicationRepository;
        this.orderRepository = orderRepository;
        this.objectMapper = objectMapper;
        this.projectProductMappingRepository = projectProductMappingRepository;
    }

    /**
     * 定义切点，匹配所有在entrance包及其子包下的方法。
     */
    @Pointcut("within(com.maguo.loan.cash.flow.entrance..*)")
    public void inEntrancePackage() {
    }

    /**
     * 前置通知：在目标方法执行前，记录借款请求到数据库。
     */
    @Before("inEntrancePackage() && @annotation(logLoanRequest)")
    public void beforeLoanAdvice(JoinPoint joinPoint, LogLoanRequest logLoanRequest) {
        try {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
            String requestBody = (String) request.getAttribute("decryptedRequestBody");
            if (requestBody == null || requestBody.isEmpty()) {
                return;
            }
            LoanApplication application = buildLoanApplicationFromRequest(requestBody, logLoanRequest.flowIdentifier());
            application.setLoanStatus(ProcessState.PROCESSING);
            LoanApplication saved = loanApplicationRepository.save(application);
            LOAN_APPLICATION_LOG_ID.set(saved.getId());
            logger.info("借款请求已入库, ID: {}", saved.getId());
        } catch (Exception e) {
            logger.error("借款请求入库（@Before）失败", e);
            LOAN_APPLICATION_LOG_ID.remove();
        }
    }

    /**
     * 成功返回通知：更新状态为“成功”。
     */
    @AfterReturning("inEntrancePackage() && @annotation(com.maguo.loan.cash.flow.annotation.LogLoanRequest)")
    public void afterReturningLoanAdvice() {
        updateStatus(ProcessState.SUCCESS, null);
    }

    /**
     * 异常抛出通知：更新状态为“失败”，并记录原因。
     */
    @AfterThrowing(pointcut = "inEntrancePackage() && @annotation(com.maguo.loan.cash.flow.annotation.LogLoanRequest)", throwing = "ex")
    public void afterThrowingLoanAdvice(Throwable ex) {
        updateStatus(ProcessState.FAIL, ex.getMessage());
    }

    private void updateStatus(ProcessState status, String reason) {
        String id = LOAN_APPLICATION_LOG_ID.get();
        if (id == null) {
            return;
        }
        try {
            loanApplicationRepository.findById(id).ifPresent(app -> {
                app.setLoanStatus(status);
                if (reason != null) {
                    app.setFailReason(reason.substring(0, Math.min(reason.length(), MAX_BYTE_VALUE)));
                }
                loanApplicationRepository.save(app);
                logger.info("更新借款记录状态为: {}, ID: {}", status, id);
            });
        } catch (Exception e) {
            logger.error("更新借款记录状态失败, ID: {}", id, e);
        } finally {
            LOAN_APPLICATION_LOG_ID.remove();
        }
    }

    /**
     * 从请求JSON中提取数据构建借款申请实体。
     * 注意：这里的解析逻辑需要你根据实际的借款请求报文来调整。
     */
    private LoanApplication buildLoanApplicationFromRequest(String requestBody, String flowIdentifier) {
        try {
            LoanApplication app = new LoanApplication();
            JsonNode rootNode = objectMapper.readTree(requestBody);
            app.setApplyTime(LocalDateTime.now());
            if (FlowChannel.LVXIN.name().equals(flowIdentifier)) {
                app.setExternalOrderNo(rootNode.path("partnerUserId").asText());
                app.setIdType("ID_CARD");
                app.setLoanAmount(new BigDecimal(rootNode.path("loanAmount").asText("0")));
                app.setCustomerName(rootNode.path("syncBindCard").path("acctName").asText());
                app.setIdNo(rootNode.path("syncBindCard").path("idNo").asText());
                app.setCustomerName(rootNode.path("syncBindCard").path("acctName").asText("未知"));
                app.setMobile(rootNode.path("syncBindCard").path("phoneNo").asText());
                app.setBankAcct(rootNode.path("syncBindCard").path("acctNo").asText());
                app.setBankCode(rootNode.path("syncBindCard").path("bankCode").asText());

                app.setLoanTerm(rootNode.path("period").asInt(0));
                Integer calculatedAge = AgeUtil.calculateAge(app.getIdNo());
                app.setAge(calculatedAge);
                String gender = GioPushUtil.getGenderFromIDCard(app.getIdNo());
                app.setGender(gender);
                app.setFlowChannel(FlowChannel.LVXIN.name());
                List<Order> orderList = orderRepository.findByAllOuterOrderId(app.getExternalOrderNo());

                String bankChannel = orderList.get(0).getApplyChannel();
                app.setProductCode(orderList.get(0).getApplyChannel());
                String channelName;
                switch (bankChannel) {
                    case "01":
                    case "02":
                        channelName = "CYBK";
                        break;
                    case "03":
                    case "04":
                        channelName = "HXBK";
                        break;
                    default:
                        channelName = "未知渠道";
                        break;
                }
                Optional<ProjectProductMapping> productMapping = projectProductMappingRepository.findByProductCode(bankChannel);
                if (productMapping.isPresent()) {
                    app.setProjectCode(productMapping.get().getProjectCode());
                } else {
                    app.setProjectCode("");
                }
                app.setBankChannel(channelName);
                logger.info("bankChannel={}, channelName={}", bankChannel, channelName);
            } else if (FlowChannel.PPCJDL.name().equals(flowIdentifier)) {

                app.setCustomerName(rootNode.path("custName").asText("未知"));
                app.setIdType("ID_CARD");
                app.setExternalOrderNo(rootNode.path("loanReqNo").asText());
                app.setIdNo(rootNode.path("idNo").asText());
                app.setMobile(rootNode.path("mobileNo").asText());
                String sourceCode = rootNode.path("sourceCode").asText();
                String accessType = rootNode.path("accessType").asText();
                app.setProductCode(sourceCode + "-" + accessType);
                app.setLoanAmount(new BigDecimal(rootNode.path("loanAmt").asText("0")));
                app.setLoanTerm(rootNode.path("loanTerm").asInt(0));
                Integer calculateAge = AgeUtil.calculateAge(app.getIdNo());
                app.setAge(calculateAge);
                String gender = GioPushUtil.getGenderFromIDCard(app.getIdNo());
                app.setGender(gender);
                app.setFlowChannel(FlowChannel.PPCJDL.name());
                app.setBankAcct(rootNode.path("bankAcct").asText());
                app.setBankCode(rootNode.path("bankCode").asText());
                String channelName;

                if (sourceCode.contains("HB")) {
                    channelName = "HXBK";
                } else if (sourceCode.contains("CY")) {
                    channelName = "CYBK";
                } else {
                    channelName = "未知渠道";
                }
                String productMapping = sourceCode + "_" + accessType;
                Optional<ProjectProductMapping> product = projectProductMappingRepository.findByProductCode(productMapping);
                if (product.isPresent()) {
                    app.setProjectCode(product.get().getProjectCode());
                } else {
                    app.setProjectCode("");
                }
                app.setBankChannel(channelName);
                logger.info("sourceCode={}, channelName={}", sourceCode, channelName);
            } else {
                logger.warn("未知的流量标识: {}, 无法解析业务数据。", flowIdentifier);
                return null;
            }

            return app;
        } catch (IOException e) {
            logger.error("解析请求JSON失败, flowIdentifier: {}, body: {}", flowIdentifier, requestBody, e);
            return null;
        }
    }
}
