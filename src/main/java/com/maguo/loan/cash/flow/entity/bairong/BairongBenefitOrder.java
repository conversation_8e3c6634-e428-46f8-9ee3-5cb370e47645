package com.maguo.loan.cash.flow.entity.bairong;

import com.maguo.loan.cash.flow.entity.BaseEntity;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 权益订单表
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Entity
@Table(name = "bairong_benefit_order")
public class BairongBenefitOrder extends BaseEntity {

    /**
     * 资金方借据号
     * 是否必填：Y（是）
     */
    private String loanNo;

    /**
     * 渠道放款流水号
     * 是否必填：Y（是）
     */
    private String outLoanSeq;

    /**
     * 权益方订单号
     * 是否必填：Y（是）
     */
    private String benefitOrderNo;

    /**
     * 权益费金额
     * 是否必填：Y（是）
     */
    private Double orderAmount;

    /**
     * 订单状态
     * 是否必填：Y（是）
     * 1-待支付【不会出现该状态】、2-支付成功、3-支付失败、4-退款中【不会出现该状态】
     * 5-退款成功、6-退款失败、7-订单取消
     */
    private Integer status;

    /**
     * 订单创建时间
     * 是否必填：Y（是）
     * 毫秒级时间戳（如：1716888888888）
     */
    private Long createTime;

    /**
     * 支付时间
     * 是否必填：N（否）
     * 毫秒级时间戳，仅“支付成功”状态时传递
     */
    private Long payTime;

    /**
     * 支付流水号
     * 是否必填：N（否）
     * 仅“支付成功”状态时传递，唯一标识支付记录
     */
    private String paymentNo;

    /**
     * 支付通道
     * 是否必填：Y（是）
     * BAOFU-宝付支付、ALLINPAY-通联支付、YEEPAY-易宝支付、JDPAY-京东支付
     * DXMPAY-度小满支付、KQIANPAY-快钱支付
     */
    private String paymentChannel;

    /**
     * 订单过期时间
     * 是否必填：N（否）
     * 毫秒级时间戳，标识订单失效时间
     */
    private Long expireTime;

    /**
     * 机构分账金额
     * 是否必填：N（否）
     * 机构可获得的分账金额（单位：元）
     */
    private Double splitAccountAmount;

    /**
     * 机构分账退款金额
     * 是否必填：N（否）
     * 机构分账金额对应的退款总金额（单位：元）
     */
    private Double shareRefundAmount;

    /**
     * 支付失败原因
     * 是否必填：N（否）
     * 仅“支付失败”状态时传递，记录最新一次扣款失败的原因
     */
    private String failReason;

    /**
     * 权益服务商
     * 是否必填：Y（是）
     * 可选值：1-钛翮科技、2-众奚科技、3-及未科技
     */
    private String benefitServiceProvider;

    //机构多商户分账信息
        /**
         * 机构分账商户号
         * 是否必填：N（否）
         * 由“权益方+融担方”标识的商户号
         */
        private String partnerShareMerchantNo;

        /**
         * 机构分账金额
         * 是否必填：N（否）
         * 该商户可获得的分账金额（单位：元）
         */
        private Double partnerShareAmount;

        /**
         * 机构分账退款金额
         * 是否必填：N（否）
         * 该商户分账金额对应的退款金额（单位：元）
         */
        private Double partnerShareRefundAmount;

    //多次退款信息
        /**
         * 退款单号
         * 是否必填：N（否）
         * 订单存在多次退款时必传，唯一标识单笔退款记录
         */
        private String refundNo;

        /**
         * 退款时间
         * 是否必填：N（否）
         * 毫秒级时间戳，标识该笔退款的执行时间
         */
        private Long refundTime;

        /**
         * 退款金额
         * 是否必填：N（否）
         * 订单存在多次退款时必传，该笔退款的金额（单位：元）
         */
        private Double refundAmount;

        /**
         * 退款途径
         * 是否必填：N（否）
         * 可选值：0-线上退款、1-线下退款
         */
        private Integer refundMethod;
    }

