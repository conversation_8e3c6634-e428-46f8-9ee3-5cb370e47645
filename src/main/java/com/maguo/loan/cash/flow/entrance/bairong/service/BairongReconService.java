package com.maguo.loan.cash.flow.entrance.bairong.service;

import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.entity.BairongRepayApplyRecord;
import com.maguo.loan.cash.flow.entity.BankRepayRecord;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.bairong.vo.BairongLoanDetailVo;
import com.maguo.loan.cash.flow.entity.bairong.vo.BairongRepaymentDetailVo;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RepayMode;
import com.maguo.loan.cash.flow.repository.BankRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.repository.bairong.BairongRepayApplyRecordRepository;
import com.maguo.loan.cash.flow.service.PPDReconService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 百融对账服务
 * @date 2025/9/15 10:11
 */
@Service
public class BairongReconService {
    private static final Logger logger = LoggerFactory.getLogger(PPDReconService.class);

    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;
    @Autowired
    private BairongRepayApplyRecordRepository bairongRepayApplyRecordRepository;
    @Autowired
    private PreOrderRepository preOrderRepository;
    @Autowired
    private BankRepayRecordRepository bankRepayRecordRepository;

    // 处理 null 的字段
    public static String safe(String val) {
        return val == null ? "" : val;
    }

    public static String safe(BigDecimal val) {
        return val == null ? "" : val.toPlainString();
    }

    /**
     * 获取放款对账信息
     *
     * @param localDate
     * @param flowChannel
     * @param bankChannel
     * @return
     */
    public List<BairongLoanDetailVo> getLoanDetailReconFile(LocalDate localDate, FlowChannel flowChannel, BankChannel bankChannel) {
        List<BairongLoanDetailVo> loanInfoVoList = new ArrayList<>();
        LocalDateTime yesterdayStart = localDate.atStartOfDay(); // 当天 00:00:00
        LocalDateTime yesterdayEnd = localDate.atTime(LocalTime.MAX); // 当天 23:59:59
        //todo 查询loan 还是百融loan表
        List<Loan> loanList = loanRepository.findByLoanTimeBetweenAndLoanStateAndFlowChannelAndBankChannel(yesterdayStart, yesterdayEnd, ProcessState.SUCCEED, flowChannel, bankChannel);
        if (loanList != null && !loanList.isEmpty()) {
            for (Loan loan : loanList) {
                BairongLoanDetailVo loanInfoVo = new BairongLoanDetailVo();
                loanInfoVo.setOutApplSeq("");
                Optional<PreOrder> byOrderNo = preOrderRepository.findByOrderNo(loan.getOuterLoanId());
                loanInfoVo.setApplyDt(loan.getApplyTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                loanInfoVo.setLoanNo("");
                loanInfoVo.setDnAmt(BigDecimal.ZERO);
                loanInfoVo.setApplyTnr(1);
                loanInfoVo.setBasicIntRat(BigDecimal.ZERO);
                loanInfoVo.setLoanActvDt("");
                loanInfoVo.setAcctName("");
                loanInfoVo.setAcctBank("");
                loanInfoVo.setAcctNo("");
                loanInfoVoList.add(loanInfoVo);
            }
        }
        return loanInfoVoList;
    }

    /**
     * 获取还款对账信息
     *
     * @param localDate
     * @param flowChannel
     * @param bankChannel
     * @return
     */
    public List<BairongRepaymentDetailVo> getRepayDetailReconFile(LocalDate localDate, FlowChannel flowChannel, BankChannel bankChannel) {
        List<BairongRepaymentDetailVo> repaymentInfoVoList = new ArrayList<>();
        LocalDateTime yesterdayStart = localDate.atStartOfDay();
        LocalDateTime yesterdayEnd = localDate.atTime(LocalTime.MAX);
        List<CustomRepayRecord> repayRecordList = customRepayRecordRepository.findByRepaidDateBetweenAndRepayState(yesterdayStart, yesterdayEnd, ProcessState.SUCCEED);
        if (repayRecordList != null && !repayRecordList.isEmpty()) {
            for (CustomRepayRecord repayRecord : repayRecordList) {
                Loan loan = loanRepository.findByIdAndFlowChannelAndBankChannel(repayRecord.getLoanId(), flowChannel, bankChannel).orElse(null);
                if (Objects.isNull(loan)) {
                    continue;
                }
                BairongRepaymentDetailVo bairongRepaymentDetailVo = new BairongRepaymentDetailVo();
                bairongRepaymentDetailVo.setLoanNo(loan.getLoanRecordId());
                BankRepayRecord bankRepayRecord = bankRepayRecordRepository.findBySourceRecordId(repayRecord.getId());
                if (Objects.isNull(bankRepayRecord)) continue;
                bairongRepaymentDetailVo.setRepaymentSeq(bankRepayRecord.getId());
                //todo 待对客表字段更新
                BairongRepayApplyRecord bairongRepayApplyRecord = new BairongRepayApplyRecord();
                bairongRepaymentDetailVo.setSetlDt(repayRecord.getRepaidDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                bairongRepaymentDetailVo.setTotalAmt(repayRecord.getTotalAmt());
                bairongRepaymentDetailVo.setPrcpAmt(repayRecord.getPrincipalAmt());
                bairongRepaymentDetailVo.setIntAmt(repayRecord.getInterestAmt());
                bairongRepaymentDetailVo.setOdIntAmt(bankRepayRecord.getPenalty());
                bairongRepaymentDetailVo.setCommOdIntAmt(BigDecimal.ZERO); //repayRecord.getCompensatoryAmt()
                bairongRepaymentDetailVo.setFeeAmt(bankRepayRecord.getConsultFee());
                //todo 待对客表字段更新
                bairongRepaymentDetailVo.setIsDc("");
                bairongRepaymentDetailVo.setGuaranteeFeeAmt(repayRecord.getPrincipalAmt());
                bairongRepaymentDetailVo.setPenlAmt(repayRecord.getPenaltyAmt());
                //todo 待对客表字段更新
                bairongRepaymentDetailVo.setOtherAmt(BigDecimal.ZERO); //repayRecord.getOtherAmt()
                bairongRepaymentDetailVo.setDeductAmt(repayRecord.getReduceAmount());
                //todo 待对客表字段更新
                bairongRepaymentDetailVo.setPlatformFlowNo("");
                if (repayRecord.getRepayMode().equals(RepayMode.ONLINE)) {
                    bairongRepaymentDetailVo.setOutApplSeq(bairongRepayApplyRecord.getOutRepayId());
                    bairongRepaymentDetailVo.setPpErInd("1");
                    bairongRepaymentDetailVo.setPayChannel("");
                } else if (repayRecord.getRepayMode().equals(RepayMode.OFFLINE)) {
                    bairongRepaymentDetailVo.setPpErInd("3");
                    bairongRepaymentDetailVo.setPayChannel("");
                } else {
                    bairongRepaymentDetailVo.setPpErInd("2");
                    bairongRepaymentDetailVo.setPayChannel("");
                }
                repaymentInfoVoList.add(bairongRepaymentDetailVo);

            }
        }
        return repaymentInfoVoList;
    }
}
