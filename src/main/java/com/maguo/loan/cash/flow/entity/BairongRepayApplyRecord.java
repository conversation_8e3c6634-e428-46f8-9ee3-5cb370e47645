package com.maguo.loan.cash.flow.entity;

import com.maguo.loan.cash.flow.enums.WhetherState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;


/**
 * <AUTHOR>
 */
@Entity
@Table(name = "bairong_repay_apply_record")
public class BairongRepayApplyRecord extends BaseEntity {

    @Override
    protected String prefix() {
        return "BAIRONG";
    }

    private String loanId;

    private String outLoanId;

    private String outCreditId;

    private String outRepayId;

    private String repayCardId;

    private String sourceCode;

    private String period;

    private String repayType;

    @Enumerated(EnumType.STRING)
    private WhetherState needSmsCode;

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getOutLoanId() {
        return outLoanId;
    }

    public void setOutLoanId(String outLoanId) {
        this.outLoanId = outLoanId;
    }

    public String getOutCreditId() {
        return outCreditId;
    }

    public void setOutCreditId(String outCreditId) {
        this.outCreditId = outCreditId;
    }

    public String getOutRepayId() {
        return outRepayId;
    }

    public void setOutRepayId(String outRepayId) {
        this.outRepayId = outRepayId;
    }

    public String getRepayCardId() {
        return repayCardId;
    }

    public void setRepayCardId(String repayCardId) {
        this.repayCardId = repayCardId;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public WhetherState getNeedSmsCode() {
        return needSmsCode;
    }

    public void setNeedSmsCode(WhetherState needSmsCode) {
        this.needSmsCode = needSmsCode;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }

}
