package com.maguo.loan.cash.flow.entrance.bairong.config;

import com.maguo.loan.cash.flow.common.RequestUriLogFilter;
import com.maguo.loan.cash.flow.entrance.bairong.filter.BairongEncryptFilter;
import jakarta.servlet.Filter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.CompositeFilter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 百融过滤器配置
 * @date 2025/9/10 15:05
 */
@Configuration
public class BairongFilterConfig {

    @Bean
    public FilterRegistrationBean<CompositeFilter> bairongFilter(BairongConfig baiRongConfig) {
        List<Filter> filterList = new ArrayList<>();
        filterList.add(new RequestUriLogFilter());
        filterList.add(new BairongEncryptFilter(baiRongConfig));
        //将多个过滤器组合成一个
        CompositeFilter compositeFilter = new CompositeFilter();
        compositeFilter.setFilters(filterList);
        FilterRegistrationBean<CompositeFilter> bean = new FilterRegistrationBean<>(compositeFilter);
        bean.addUrlPatterns("/bairong/*");
        return bean;
    }
}
