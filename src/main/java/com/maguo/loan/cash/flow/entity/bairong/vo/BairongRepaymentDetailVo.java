package com.maguo.loan.cash.flow.entity.bairong.vo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 百融还款明细
 * @date 2025/9/15 9:17
 */
public class BairongRepaymentDetailVo {


    /**
     * 资金方借据号
     */
    private String loanNo;

    /**
     * 资金方还款流水号
     */
    private String repaymentSeq;

    /**
     * 渠道还款流水号
     */
    private String outApplSeq;

    /**
     * 还款模式
     * 0：当期还款
     * 1：逾期还款
     * 2：提前结清
     * 6：提前还当期
     */
    private String payMode;

    /**
     * 实际扣款日期 yyyy-MM-dd
     */
    private String setlDt;

    /**
     * 还款方式
     * 1：线上主动还款
     * 2：批扣还款
     * 3：线下还款
     */
    private String ppErInd;

    /**
     * 还款金额
     */
    private BigDecimal totalAmt;

    /**
     * 归还本金
     */
    private BigDecimal prcpAmt;

    /**
     * 归还利息
     */
    private BigDecimal intAmt;

    /**
     * 归还罚息
     */
    private BigDecimal odIntAmt;

    /**
     * 归还复利
     */
    private BigDecimal commOdIntAmt;

    /**
     * 归还费用
     */
    private BigDecimal feeAmt;

    /**
     * 是否代偿
     * Y 是
     * N 否
     */
    private String isDc;

    /**
     * 归还融担费
     */
    private BigDecimal guaranteeFeeAmt;

    /**
     * 归还违约金
     */
    private BigDecimal penlAmt;

    /**
     * 归还其它项
     */
    private BigDecimal otherAmt;

    /**
     * 减免金额
     */
    private BigDecimal deductAmt;

    /**
     * 商户订单号
     * 资金方扣款时，发送给支付渠道的商户订单号
     */
    private String platformFlowNo;

    /**
     * 扣款渠道
     */
    private String payChannel;

    // Getter和Setter方法
    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getRepaymentSeq() {
        return repaymentSeq;
    }

    public void setRepaymentSeq(String repaymentSeq) {
        this.repaymentSeq = repaymentSeq;
    }

    public String getOutApplSeq() {
        return outApplSeq;
    }

    public void setOutApplSeq(String outApplSeq) {
        this.outApplSeq = outApplSeq;
    }

    public String getPayMode() {
        return payMode;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public String getSetlDt() {
        return setlDt;
    }

    public void setSetlDt(String setlDt) {
        this.setlDt = setlDt;
    }

    public String getPpErInd() {
        return ppErInd;
    }

    public void setPpErInd(String ppErInd) {
        this.ppErInd = ppErInd;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public BigDecimal getPrcpAmt() {
        return prcpAmt;
    }

    public void setPrcpAmt(BigDecimal prcpAmt) {
        this.prcpAmt = prcpAmt;
    }

    public BigDecimal getIntAmt() {
        return intAmt;
    }

    public void setIntAmt(BigDecimal intAmt) {
        this.intAmt = intAmt;
    }

    public BigDecimal getOdIntAmt() {
        return odIntAmt;
    }

    public void setOdIntAmt(BigDecimal odIntAmt) {
        this.odIntAmt = odIntAmt;
    }

    public BigDecimal getCommOdIntAmt() {
        return commOdIntAmt;
    }

    public void setCommOdIntAmt(BigDecimal commOdIntAmt) {
        this.commOdIntAmt = commOdIntAmt;
    }

    public BigDecimal getFeeAmt() {
        return feeAmt;
    }

    public void setFeeAmt(BigDecimal feeAmt) {
        this.feeAmt = feeAmt;
    }

    public String getIsDc() {
        return isDc;
    }

    public void setIsDc(String isDc) {
        this.isDc = isDc;
    }

    public BigDecimal getGuaranteeFeeAmt() {
        return guaranteeFeeAmt;
    }

    public void setGuaranteeFeeAmt(BigDecimal guaranteeFeeAmt) {
        this.guaranteeFeeAmt = guaranteeFeeAmt;
    }

    public BigDecimal getPenlAmt() {
        return penlAmt;
    }

    public void setPenlAmt(BigDecimal penlAmt) {
        this.penlAmt = penlAmt;
    }

    public BigDecimal getOtherAmt() {
        return otherAmt;
    }

    public void setOtherAmt(BigDecimal otherAmt) {
        this.otherAmt = otherAmt;
    }

    public BigDecimal getDeductAmt() {
        return deductAmt;
    }

    public void setDeductAmt(BigDecimal deductAmt) {
        this.deductAmt = deductAmt;
    }

    public String getPlatformFlowNo() {
        return platformFlowNo;
    }

    public void setPlatformFlowNo(String platformFlowNo) {
        this.platformFlowNo = platformFlowNo;
    }

    public String getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }

    // toString方法
    @Override
    public String toString() {
        return "RepaymentInfoVO{" +
            "loanNo='" + loanNo + '\'' +
            ", repaymentSeq='" + repaymentSeq + '\'' +
            ", outApplSeq='" + outApplSeq + '\'' +
            ", payMode='" + payMode + '\'' +
            ", setlDt='" + setlDt + '\'' +
            ", ppErInd='" + ppErInd + '\'' +
            ", totalAmt=" + totalAmt +
            ", prcpAmt=" + prcpAmt +
            ", intAmt=" + intAmt +
            ", odIntAmt=" + odIntAmt +
            ", commOdIntAmt=" + commOdIntAmt +
            ", feeAmt=" + feeAmt +
            ", isDc='" + isDc + '\'' +
            ", guaranteeFeeAmt=" + guaranteeFeeAmt +
            ", penlAmt=" + penlAmt +
            ", otherAmt=" + otherAmt +
            ", deductAmt=" + deductAmt +
            ", platformFlowNo='" + platformFlowNo + '\'' +
            ", payChannel='" + payChannel + '\'' +
            '}';
    }
}
