package com.maguo.loan.cash.flow.controller;


import com.jinghang.ppd.api.RepayApi;
import com.jinghang.ppd.api.dto.RepayTrailDto;
import com.jinghang.ppd.api.dto.RestResult;
import com.jinghang.ppd.api.dto.ResultCode;
import com.jinghang.ppd.api.dto.TrailResultDto;
import com.jinghang.ppd.api.dto.repay.ReduceApplyDto;
import com.jinghang.ppd.api.dto.repay.RepayApplyDto;
import com.jinghang.ppd.api.dto.repay.RepayPlanQueryRequestDTO;
import com.jinghang.ppd.api.dto.repay.RepayPlanQueryResponseDTO;
import com.jinghang.ppd.api.dto.repay.SettleTrialDto;
import com.maguo.loan.cash.flow.convert.ManageConvert;
import com.maguo.loan.cash.flow.dto.OfflineRepayApplyRequest;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.OutWithholdFlow;
import com.maguo.loan.cash.flow.enums.CommonWithholdType;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RepayMode;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.WriteOffTypeEnum;
import com.maguo.loan.cash.flow.service.LoanService;
import com.maguo.loan.cash.flow.service.OutWithholdFlowService;
import com.maguo.loan.cash.flow.service.RepayPublicCheckService;
import com.maguo.loan.cash.flow.service.RepayService;
import com.maguo.loan.cash.flow.vo.RepayTrialRequest;
import com.maguo.loan.cash.flow.vo.TrialResultVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * 还款
 */
@RestController
@RequestMapping("manage/repay")
public class ManageRepayController implements RepayApi {
    private static final Logger logger = LoggerFactory.getLogger(ManageRepayController.class);


    @Autowired
    private RepayService repayService;
    @Autowired
    private LoanService loanService;
    @Autowired
    private RepayPublicCheckService repayPublicCheckService;
    @Autowired
    private OutWithholdFlowService outWithholdFlowService;



    /**
     * 还款试算
     * @param trailDto
     * @return
     */
    @Override
    public RestResult<TrailResultDto> trial(RepayTrailDto trailDto) {
        RepayTrialRequest trialRequest = ManageConvert.INSTANCE.toRepayTrialRequest(trailDto);
        TrialResultVo response = repayService.trial(trialRequest.getLoanId(), trialRequest.getRepayPurpose(), trialRequest.getPeriods(),null);
        TrailResultDto dto = ManageConvert.INSTANCE.toTrailResultDto(response);
        return RestResult.success(dto);
    }

    @Override
    public RestResult<Void> reduceApply(ReduceApplyDto reduceApplyDto) {
       // offlineRepayReduceService.reduceApply(reduceApplyDto);
        return RestResult.success(null);
    }

    @Override
    public RestResult<Void> repayApply(RepayApplyDto repayApplyReq) {
        Loan loan = loanService.findByOrderId(repayApplyReq.getOrderId());
        if (loan == null) {
            logger.error("借据不存在,orderId:{}", repayApplyReq.getOrderId());
            return RestResult.fail(ResultCode.PARAM_ILLEGAL);
        }

        try {
            // 还款时间段校验
            repayPublicCheckService.repayDateCheck(loan);
        } catch (Exception e) {
            logger.error("还款时间段校验异常,loanId:{}", loan.getId(), e);
            return RestResult.fail(ResultCode.BIZ_ERROR);
        }

        //保存外部代扣
        saveOutWithholdFlow(loan, repayApplyReq);

        OfflineRepayApplyRequest request = ManageConvert.INSTANCE.toRepayApplyRequest(repayApplyReq);
        request.setWriteOffType(WriteOffTypeEnum.DIRECT);
        request.setConsultationFeeWaiver(BigDecimal.ZERO);
        request.setPenaltyInterestWaiver(BigDecimal.ZERO);
        repayService.offline(request);
        return RestResult.success(null);
    }

    @Override
    public RestResult<RepayPlanQueryResponseDTO> queryRepayPlan(RepayPlanQueryRequestDTO repayPlanQueryRequestDTO) {
        return null;
    }

    @Override
    public RestResult<TrailResultDto> settleTrial(SettleTrialDto settleTrialDto) {
        if (settleTrialDto == null || settleTrialDto.getOrderId() == null || settleTrialDto.getOrderId().isEmpty()) {
            return RestResult.fail(ResultCode.PARAM_ILLEGAL, ResultCode.PARAM_ILLEGAL.getMsg());
        }

        Loan loan = loanService.findByOrderId(settleTrialDto.getOrderId());
        if (loan == null) {
            logger.error("结清试算失败，借据不存在, orderId:{}", settleTrialDto.getOrderId());
            return RestResult.fail(ResultCode.PARAM_ILLEGAL, ResultCode.PARAM_ILLEGAL.getMsg());
        }

        logger.info("开始结清试算, loanId: {}", loan.getId());
        TrialResultVo responseVo = repayService.trial(loan.getId(), RepayPurpose.CLEAR, null, null);

        TrailResultDto resultDto = ManageConvert.INSTANCE.toTrailResultDto(responseVo);
        return RestResult.success(resultDto);
    }


    private void saveOutWithholdFlow(Loan loan, RepayApplyDto repayApplyReq) {
        Optional<OutWithholdFlow> opt = outWithholdFlowService.findByLoanIdAndPeriodAndPayState(loan.getId(),repayApplyReq.getPeriod(),ProcessState.INIT);
        if (opt.isPresent()) {
            outWithholdFlowService.delete(opt.get());
        }
        //初始化外部代扣
        OutWithholdFlow outWithholdFlow = new OutWithholdFlow();
        outWithholdFlow.setLoanId(loan.getId());
        outWithholdFlow.setPeriod(repayApplyReq.getPeriod());
        outWithholdFlow.setPayState(ProcessState.INIT);
        outWithholdFlow.setCommonWithholdType(CommonWithholdType.FLOW_BAIRONG_CAPITAL.getDesc());
        outWithholdFlow.setRepayMode(RepayMode.OFFLINE);
        outWithholdFlowService.save(outWithholdFlow);
    }

}
