package com.maguo.loan.cash.flow.entrance.bairong.service;

import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.dto.OnlineRepayApplyRequest;
import com.maguo.loan.cash.flow.entity.BairongRebindRecord;
import com.maguo.loan.cash.flow.entity.BairongRepayApplyRecord;
import com.maguo.loan.cash.flow.entity.BankRepayRecord;
import com.maguo.loan.cash.flow.entity.BindCardRecord;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.UserBankCard;
import com.maguo.loan.cash.flow.entrance.bairong.convert.BairongConvert;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.AccInfoList;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongDeductAgreementNoSyncRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongDeductAgreementNoSyncResponse;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongRepayRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongRepayResponse;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongRepayResultRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongRepayResultResponse;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongRepayTrialRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongRepayTrialResponse;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.RepayInfoList;
import com.maguo.loan.cash.flow.entrance.bairong.enums.BairongRepayStatus;
import com.maguo.loan.cash.flow.entrance.bairong.enums.BairongRepaySyncStatus;
import com.maguo.loan.cash.flow.entrance.bairong.exception.BairongException;
import com.maguo.loan.cash.flow.entrance.bairong.exception.BairongResultCode;
import com.maguo.loan.cash.flow.entrance.ppd.enums.PpdRepayStatus;
import com.maguo.loan.cash.flow.enums.BoundSide;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.RepayState;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.remote.cardbin.CardBin;
import com.maguo.loan.cash.flow.repository.BankRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.repository.UserBankCardRepository;
import com.maguo.loan.cash.flow.repository.bairong.BairongRebindRecordRepository;
import com.maguo.loan.cash.flow.repository.bairong.BairongRepayApplyRecordRepository;
import com.maguo.loan.cash.flow.service.LockService;
import com.maguo.loan.cash.flow.service.Locker;
import com.maguo.loan.cash.flow.service.RepayService;
import com.maguo.loan.cash.flow.service.TrialService;
import com.maguo.loan.cash.flow.service.bound.CapitalCardService;
import com.maguo.loan.cash.flow.service.bound.PlatformCardService;
import com.maguo.loan.cash.flow.service.bound.exchange.ExchangeCardApplyReq;
import com.maguo.loan.cash.flow.vo.TrialResultVo;
import groovy.util.logging.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 百融还款业务服务类
 * <AUTHOR>
 */
@Slf4j
@Service
public class BairongRepayService {
    private static final Logger logger = LoggerFactory.getLogger(BairongRepayService.class);

    public static final int LOCK_WAIT_SECOND = 2;
    public static final int LOCK_RELEASE_SECOND = 8;
    @Autowired
    private LockService lockService;
    @Autowired
    private RepayService repayService;
    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private CapitalCardService capitalCardService;
    @Autowired
    private PlatformCardService platformCardService;
    @Autowired
    private RepayPlanRepository repayPlanRepository;
    @Autowired
    private UserBankCardRepository userBankCardRepository;
    @Autowired
    private BairongRebindRecordRepository bairongRebindRecordRepository;
    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;
    @Autowired
    private BairongRepayApplyRecordRepository bairongRepayApplyRecordRepository;
    @Autowired
    private BankRepayRecordRepository bankRepayRecordRepository;
    @Autowired
    private TrialService trialService;
    private static final String UNSUPPORTED_REPAYMENT_MODE = "6";

    /**
     * 还款试算
     * @param request 请求参数
     * @return 响应参数
     */
    public BairongRepayTrialResponse trail(BairongRepayTrialRequest request) {
        logger.info("百融还款试算请求参数:{}", JsonUtil.toJsonString(request));
        Loan loan = loanRepository.findByOuterLoanId(request.getLoanNo());
        if (Objects.isNull(loan)) {
            throw new BairongException(BairongResultCode.LOAN_NOT_EXIST);
        }
        String loanId = loan.getId();
        //还款时间段校验
        repayTrailCheck(loan);
        String repaymentMode = request.getRepaymentMode();//还款类型
        //检验还款类型为6时，提示：不支持该还款类型
        if (Objects.equals(repaymentMode, UNSUPPORTED_REPAYMENT_MODE)) {
            logger.info("检验还款类型为6时，提示：不支持该还款类型");
            throw new BairongException(BairongResultCode.REPAYMENT_TYPE_UNSUPPORTED);
        }
        //还款类型转换，0,1为当期，2为结清
        RepayPurpose repayPurpose = RepayPurpose.toBairongRepayType(repaymentMode);//还款类型转换
        logger.info("还款类型转换，0,1为当期，2为结清。还款类型为：" + repaymentMode + "，转换后为：" + repayPurpose);
        if (Objects.isNull(repayPurpose)) {
            logger.info("百融还款试算时，转换还款类型后为空时，说明不支持该还款类型。提示：不支持该还款类型");
            throw new BairongException(BairongResultCode.REPAYMENT_TYPE_UNSUPPORTED);
        }
        //如果是结清,传入最小未还的期次
        if (Objects.equals(repayPurpose, RepayPurpose.CLEAR)) {
            //查询这笔借据下还款计划是否还有待还的记录，没有就报错。提示：该笔借据下的还款计划没有待还的记录
            if (CollectionUtils.isEmpty(repayPlanRepository.findByLoanIdAndCustRepayStateOrderByPeriodAsc(loanId, RepayState.NORMAL))) {
                logger.info("该笔借据：[" + loanId + "]下的还款计划没有待还的记录");
                throw new BairongException(BairongResultCode.REPAY_PLAN_NO_NORMAL_ERROR);
            }
        }
        List<Integer> repayList = new ArrayList<>(Collections.singletonList(Integer.valueOf(request.getPeriod())));
        Integer repayPeriod = getMinPeriod(repayList, repayPurpose);//获取最小的期数
        logger.info("百融还款试算获取最小的期数为：" + repayPeriod);
        Optional<CustomRepayRecord> successRepayRecord = customRepayRecordRepository.findByLoanIdAndPeriodAndRepayState(
            loanId, repayPeriod, ProcessState.SUCCEED);
        if (successRepayRecord.isPresent()) {
            logger.error("百融还款试算失败，本期已还款成功,loanId:{},periods:{}", loanId, repayPeriod);
            throw new BairongException(BairongResultCode.BAIRONG_TRIAL_PERIOD_ERROR);
        }
        try {
            TrialResultVo resultVo = trialService.repayTrial(loanId, repayPurpose, repayPeriod, request.getOperateTime());
            BairongRepayTrialResponse result = BairongConvert.INSTANCE.toRepayTrailRes(resultVo);
            result.setLoanSeq(request.getLoanSeq());//资金方放款流水号
            result.setLoanNo(request.getLoanNo());//资金方借据号
            result.setOdCommOdInt(BigDecimal.ZERO);//应还复利
            result.setOdOtherAmt(BigDecimal.ZERO);//应还其它项
            result.setOnceLimitAmt(String.valueOf(BigDecimal.ZERO));//单笔限额
            result.setDayLimitAmt(String.valueOf(BigDecimal.ZERO));//日限额
            logger.info("百融还款试算返回:{}", JsonUtil.toJsonString(result));
            return result;
        } catch (Exception e) {
            logger.error("百融还款试算异常", e);
            throw new BairongException(e.getMessage(), BairongResultCode.FAILURE.getMsg());
        }
    }

    /**
     * 还款申请
     * @param request 请求参数
     * @return 响应参数
     */
    public BairongRepayResponse repay(BairongRepayRequest request) {
        BairongRepayResponse response = new BairongRepayResponse();
        checkParameters(request);//还款申请数据校验
        RepayInfoList repayInfoList = request.getRepayInfoList();//账单信息
        String outerLoanId = repayInfoList.getLoanNo();//资金方借据号
        String repaymentGid = repayInfoList.getOutBatchRepaymentSeq();//渠道还款流水号
        response.setOutBatchRepaymentSeq(repaymentGid);
        //检查重复提交
        boolean existsRecord = bairongRepayApplyRecordRepository.existsByOutRepayId(repaymentGid);
        if (existsRecord) {
            logger.error("百融还款申请，重复提交，outerLoanId:{},outRepayId:{}", outerLoanId, repaymentGid);
            CustomRepayRecord repayRecord = customRepayRecordRepository.findByOuterRepayNo(repaymentGid);
            if (Objects.isNull(repayRecord)) {
                response.setBillStatus(BairongRepayStatus.FAIL.getCode());
                response.setFailReason(BairongRepayStatus.FAIL.getDesc());
            } else {
                response.setBillStatus(PpdRepayStatus.SUCCESS.getCode());
                response.setFailReason(PpdRepayStatus.SUCCESS.getDesc());
            }
            return response;
        }
        Loan loan = loanRepository.findByOuterLoanId(outerLoanId);
        if (Objects.isNull(loan)) {
            throw new BairongException(BairongResultCode.LOAN_NOT_EXIST);
        }
        String loanId = loan.getId();
        //期数集合
        List<Integer> repayList = List.of(Integer.valueOf(repayInfoList.getPeriod()));
        //还款类型转换
        RepayPurpose repayPurpose = RepayPurpose.toBairongRepayType(repayInfoList.getRepaymentMode());
        if (Objects.isNull(repayPurpose)) {
            logger.info("百融还款申请时，转换还款类型后为空时，说明不支持该还款类型。提示：不支持该还款类型");
            throw new BairongException(BairongResultCode.REPAYMENT_TYPE_UNSUPPORTED);
        }
        Integer period = getMinPeriod(repayList, repayPurpose);//获取最小的期数
        logger.info("百融还款申请获取最小的期数为：" + period);
        Optional<CustomRepayRecord> customRepayRecord = customRepayRecordRepository.findByLoanIdAndPeriodAndRepayState(loanId, period, ProcessState.SUCCEED);
        if (customRepayRecord.isPresent()) {
            logger.error("百融还款申请失败，本期已还款成功,loanId:{},periods:{}", loanId, repayList);
            throw new BairongException(BairongResultCode.BAIRONG_REPAY_PERIOD_ERROR);
        }
        response.setBillStatus(PpdRepayStatus.SUCCESS.getCode());
        response.setFailReason(PpdRepayStatus.SUCCESS.getDesc());
        //初始化百融还款申请记录
        BairongRepayApplyRecord repayApplyRecord = bairongRepayApplyRecordRepository.findByOutRepayId(repaymentGid).orElse(null);
        if (Objects.isNull(repayApplyRecord)) {
            //还款申请初始化申请记录时部分字段转换
            repayApplyRecord = BairongConvert.INSTANCE.toRepayApplyRecord(request.getAccInfoList(), repayInfoList);
            repayApplyRecord.setLoanId(loan.getId());
            repayApplyRecord.setNeedSmsCode(WhetherState.N);
            repayApplyRecord.setRepayType(repayInfoList.getRepaymentMode());
            logger.info("初始化百融还款申请记录信息为:{}", JsonUtil.toJsonString(repayApplyRecord));
            bairongRepayApplyRecordRepository.save(repayApplyRecord);
        }
        //查询用户还款卡信息
        UserBankCard userBankCard = userBankCardRepository.findById(loan.getRepayCardId()).orElseThrow();
        logger.info("通过放款记录的还款卡id" + loan.getRepayCardId() + "查询用户还款卡信息为:{}", JsonUtil.toJsonString(userBankCard));
        String acctNo = request.getAccInfoList().getAcctNo();
        //检验还款卡信息为空或不为空且银行卡账号不一致时，提示：存在贷后换绑卡情况，银行卡账号不一致。请先同步协议号后再进行还款！
        if (ObjectUtils.isEmpty(userBankCard) || ObjectUtils.isNotEmpty(userBankCard)
            && !Objects.equals(acctNo, userBankCard.getCardNo())) {
            logger.info("还款卡信息为空或不为空且银行卡账号不一致时，提示：存在贷后换绑卡情况，银行卡账号不一致。请先同步协议号后再进行还款！");
            throw new BairongException(BairongResultCode.ACCT_NO_CHECK_ERROR);
        }
        //发起还款
        bairongRepay(request, period);
        return response;
    }

    /**
     * 发起还款
     * @param request 请求参数
     * @param period  期数
     */
    private void bairongRepay(BairongRepayRequest request, Integer period) {
        RepayInfoList repayInfoList = request.getRepayInfoList();//账单信息
        Loan loan = loanRepository.findByOuterLoanId(repayInfoList.getLoanNo());
        if (Objects.isNull(loan)) {
            throw new BairongException(BairongResultCode.LOAN_NOT_EXIST);
        }
        String loanId = loan.getId();
        RepayPurpose repayPurpose = RepayPurpose.toBairongRepayType(repayInfoList.getRepaymentMode());//还款类型转换
        //默认线上还款逻辑
        OnlineRepayApplyRequest repayApplyRequest = BairongConvert.INSTANCE.toOnlineApplyRequest(loanId, period, repayPurpose);
        repayApplyRequest.setOtuRepayNo(repayInfoList.getOutBatchRepaymentSeq());//渠道还款流水号
        repayApplyRequest.setConsultationFeeWaiver(BigDecimal.ZERO);//咨询费减免
        repayApplyRequest.setPenaltyInterestWaiver(BigDecimal.ZERO);//罚息减免
        repayApplyRequest.setRepayTotalAmount(repayInfoList.getCurentTotalOdAmt());//本次总计还款金额
        logger.info("百融线上还款申请请求参数:{}", JsonUtil.toJsonString(repayApplyRequest));
        repayService.online(repayApplyRequest);
        logger.info("百融线上还款申请请求响应,loanId为:{}", loanId);
    }

    /**
     * 还款申请数据校验
     * @param request 请求数据
     */
    private static void checkParameters(BairongRepayRequest request) {
        AccInfoList accInfoList = request.getAccInfoList();//账号信息
        RepayInfoList repayInfoList = request.getRepayInfoList();//账单信息
        if (Objects.isNull(accInfoList)) {
            logger.info("百融还款申请时，账号信息不能为空！");
            throw new BairongException(BairongResultCode.ACC_INFO_LIST_IS_NULL_ERROR);
        }
        if (Objects.isNull(repayInfoList)) {
            logger.info("百融还款申请时，账单信息不能为空！");
            throw new BairongException(BairongResultCode.REPAY_INFO_LIST_IS_NULL_ERROR);
        }
        String repaymentMode = repayInfoList.getRepaymentMode();//还款类型
        //检验还款类型为6时，提示：不支持该还款类型
        if (Objects.equals(repaymentMode, UNSUPPORTED_REPAYMENT_MODE)) {
            logger.info("检验还款类型为6时，提示：不支持该还款类型");
            throw new BairongException(BairongResultCode.REPAYMENT_TYPE_UNSUPPORTED);
        }
    }

    /**
     * 获取最小的期数
     * @param repayList    期数集合
     * @param repayPurpose 还款类型
     * @return 最小的期数
     */
    private int getMinPeriod(List<Integer> repayList, RepayPurpose repayPurpose) {
        //当期还款只传1期
        Integer repayPeriod = repayList.get(0);
        if (Objects.equals(repayPurpose, RepayPurpose.CLEAR)) {
            //取最早的一期
            repayList = repayList.stream().sorted().toList();
            repayPeriod = repayList.get(0);
        }
        return repayPeriod;
    }

    /**
     * 还款时间段校验
     * @param loan 放款记录信息
     */
    private void repayTrailCheck(Loan loan) {
        if (loan.getLoanTime().toLocalDate().isEqual(LocalDate.now())) {
            logger.info("放款日当天不允许发起还款");
            throw new BairongException(BairongResultCode.REPAY_NOT_SUPPORTED_LOAN_DATE);
        }
    }

    /**
     * 还款结果查询
     * @param request 请求参数
     * @return 响应参数
     */
    public BairongRepayResultResponse repayQuery(BairongRepayResultRequest request) {
        BairongRepayResultResponse result = new BairongRepayResultResponse();
        BairongRepayApplyRecord baiRongRepayApplyRecord = bairongRepayApplyRecordRepository.findByOutRepayId(request.getOutBatchRepaymentSeq()).orElseThrow();
        CustomRepayRecord customRepayRecord = customRepayRecordRepository.findByOuterRepayNo(baiRongRepayApplyRecord.getOutRepayId());
        if (Objects.isNull(customRepayRecord)) {
            logger.info("还款结果查询失败，通过还款流水号：" + request.getOutBatchRepaymentSeq() + "查询不到对应的对客还款记录");
            result.setBillStatus(BairongRepayStatus.ERROR.getCode());//还款状态 系统异常
            //失败原因(对客还款记录不存在)
            result.setFailReason(BairongRepayStatus.CUSTOM_REPAY_RECORD_IS_NULL_ERROR.getDesc());
            return result;
        }
        BankRepayRecord bankRepayRecord = bankRepayRecordRepository.findBySourceRecordId(customRepayRecord.getId());
        if (Objects.isNull(bankRepayRecord)) {
            logger.info("还款结果查询失败，通过对客还款记录的主键id：" + customRepayRecord.getId() + "查询不到对应的对资还款记录");
            result.setBillStatus(BairongRepayStatus.ERROR.getCode());//还款状态 系统异常
            //失败原因(对资还款记录不存在)
            result.setFailReason(BairongRepayStatus.BANK_REPAY_RECORD_IS_NULL_ERROR.getDesc());
            return result;
        }
        ProcessState customRepayState = customRepayRecord.getRepayState();//对客还款状态
        ProcessState state = bankRepayRecord.getState();//对资还款状态
        logger.info("======对客还款状态为：" + customRepayState + "，对资还款状态为：" + state + "。======");
        if (ProcessState.SUCCEED == customRepayState) {//01-还款成功
            result.setBillStatus(BairongRepayStatus.SUCCESS.getCode());//还款状态
            result.setFailReason(BairongRepayStatus.SUCCESS.getDesc());//失败原因
            result.setSetlValDt(bankRepayRecord.getUpdatedTime().format(DateTimeFormatter.ofPattern("yyyyMMdd")));//还款成功日期
            result.setSetlValTime(bankRepayRecord.getUpdatedTime().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));//还款成功时间
        } else if (ProcessState.FAILED == customRepayState) {//02-还款失败
            result.setBillStatus(BairongRepayStatus.FAIL.getCode());//还款状态
            result.setFailReason(BairongRepayStatus.FAIL.getDesc());//失败原因
        } else if (ProcessState.PROCESSING == customRepayState) {//03-处理中
            result.setBillStatus(BairongRepayStatus.PROCESSING.getCode());//还款状态
            result.setFailReason(BairongRepayStatus.PROCESSING.getDesc());//失败原因
        } else {
            result.setBillStatus(BairongRepayStatus.PROCESSING.getCode());//还款状态
            result.setFailReason(BairongRepayStatus.PROCESSING.getDesc());//失败原因
        }
        result.setOutBatchRepaymentSeq(request.getOutBatchRepaymentSeq());//渠道还款流水号
        result.setBatchRepaymentSeq(
            Objects.nonNull(request.getSetlSeq()) ? request.getSetlSeq() : bankRepayRecord.getId());//资金方还款流水号
        result.setLoanNo(bankRepayRecord.getLoanId());//资金方借据号
        result.setApplyRePayAmt(bankRepayRecord.getAmount());//还款金额
        result.setPrinAmt(bankRepayRecord.getPrincipal());//实还本金
        result.setIntAmt(bankRepayRecord.getInterest());//实还利息
        result.setOdIntAmt(bankRepayRecord.getPenalty());//实还罚息
        result.setFeeAmt(bankRepayRecord.getConsultFee());//实还费用
        result.setGrtAmt(bankRepayRecord.getGuarantee());//实还融担费
        result.setPenlAmt(bankRepayRecord.getBreach());//实还违约金
        result.setDeductAmt(customRepayRecord.getReduceAmount());//减免金额
        result.setCommIntAmt(BigDecimal.ZERO);//实还复利
        result.setOtherAmt(BigDecimal.ZERO);//实还其它项
        return result;
    }

    /**
     * 还款协议号同步
     * @param request 请求参数
     * @return 响应参数
     */
    public BairongDeductAgreementNoSyncResponse repayAgreementNoSync(BairongDeductAgreementNoSyncRequest request) {
        logger.info("百融还款协议号同步请求参数:{}", JsonUtil.toJsonString(request));
        BairongDeductAgreementNoSyncResponse syncResponse = new BairongDeductAgreementNoSyncResponse();
        String outSignSeq = request.getOutSignSeq();//渠道申请流水号
        String lockKey = FlowChannel.LTFQ.name() + "_bind_apply_" + outSignSeq;
        Locker lock = lockService.getLock(lockKey);
        try {
            boolean locked = lock.tryLock(Duration.ofSeconds(LOCK_WAIT_SECOND), Duration.ofSeconds(LOCK_RELEASE_SECOND));
            if (!locked) {
                logger.error("百融-乐通分期还款协议号同步，重复提交。渠道申请流水号:{}", outSignSeq);
                throw new BairongException(BairongResultCode.NO_SUBMIT_REPEAT);
            }
            //获取放款记录信息
            Loan loan = getLoanInfo(request);
            List<ProcessState> repayStates = Arrays.asList(ProcessState.SUCCESS, ProcessState.FAILED);
            //查询该笔借据下是否有还款状态不为终态的还款记录
            Optional<CustomRepayRecord> customRepayRecord = customRepayRecordRepository.findByLoanIdAndRepayState(loan.getId(), repayStates);
            //还款在途订单校验
            if (customRepayRecord.isPresent()) {
                logger.error("百融-乐通分期还款协议号同步时，loan_id为：" + loan.getId() + "的借据下有在途的还款订单，请等该还款返回终态后重试");
                throw new BairongException(BairongResultCode.REPAY_IS_PENDING_ERROR);
            }
            Order order = orderRepository.findOrderById(loan.getOrderId());
            if (Objects.isNull(order)) {
                logger.error("百融-乐通分期还款协议号同步时，通过orderId查询订单信息为空");
                throw new BairongException(BairongResultCode.ORDER_INFO_IS_NULL_ERROR);
            }
            String orderNo = order.getOuterOrderId();//外部订单号
            //查询外部订单号下状态为处理中，绑卡方为资金的百融换绑卡记录
            logger.info("查询外部订单号为：[" + orderNo + "]下的状态为处理中，绑卡方为资金的百融换绑卡记录");
            BairongRebindRecord bairongRebindRecord = bairongRebindRecordRepository.
                findByCreditIdAndStateAndBoundSide(orderNo, ProcessState.PROCESSING, BoundSide.CAPITAL);
            if (Objects.nonNull(bairongRebindRecord)) {
                logger.info("存在，更新该笔换绑卡记录状态为失败");
                bairongRebindRecord.setState(ProcessState.FAILED);
                bairongRebindRecordRepository.save(bairongRebindRecord);
            }
            //调用资金方换绑卡接口去进行协议号同步
            BindCardRecord capitalBindResult = capitalBindApply(loan, orderNo, request);
            ProcessState state = capitalBindResult.getState();//绑卡状态
            syncResponse.setOutSignSeq(outSignSeq);//渠道申请流水号
            if (Objects.equals(state, ProcessState.SUCCEED)) {//同步成功
                logger.info("还款协议号同步=======成功");
                syncResponse.setStatus(BairongRepaySyncStatus.SUCCESS.getCode());//状态：1
                syncResponse.setStatusDesc(BairongRepaySyncStatus.SUCCESS.getDesc());//状态描述：成功
                syncResponse.setSignSeq(bairongRebindRecord.getId());//资金方申请流水号
            } else {//同步失败
                logger.info("还款协议号同步=======失败");
                syncResponse.setStatus(BairongRepaySyncStatus.FAIL.getCode());//状态：2
                if (StringUtils.isNotBlank(capitalBindResult.getFailReason())) {
                    syncResponse.setStatusDesc(capitalBindResult.getFailReason());//状态描述
                } else {
                    syncResponse.setStatusDesc(BairongRepaySyncStatus.FAIL.getDesc());//状态描述:失败
                }
            }
            logger.info("百融款协议号同步返回:{}", JsonUtil.toJsonString(syncResponse));
            return syncResponse;
        } catch (Exception e) {
            logger.error("百融款协议号同步异常", e);
            throw new BairongException(e.getMessage(), BairongResultCode.FAILURE.getMsg());
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取放款记录信息
     * @param request 请求参数
     * @return 返回放款记录
     */
    private Loan getLoanInfo(BairongDeductAgreementNoSyncRequest request) {
        Loan loan = null;
        String outLoanSeq;//渠道放款流水号
        //检验outLoanSeq渠道放款流水号是否为空
        if(StringUtils.isBlank(request.getOutLoanSeq())){
            //抛异常，提示：还款协议号同步时，outLoanSeq（渠道放款流水号）不能为空
            logger.error("百融-乐通分期还款协议号同步时，outLoanSeq（渠道放款流水号）不能为空");
            throw new BairongException(BairongResultCode.OUTLOANSEQ_IS_NULL_ERROR);
        }else{
            if(StringUtils.isNotBlank(request.getOutLoanSeq())){
                outLoanSeq = request.getOutLoanSeq();
                loan = loanRepository.findByOuterLoanId(outLoanSeq);
            }
        }
        if (Objects.isNull(loan)) {
            logger.info("百融-乐通分期还款协议号同步时，借款记录不存在");
            throw new BairongException(BairongResultCode.LOAN_NOT_EXIST);
        }
        return loan;
    }

    /**
     * 调用资金方换绑卡接口去进行协议号同步
     * @param loan    放款记录
     * @param orderNo 外部订单号
     * @param request 请求参数
     * @return 同步结果信息
     */
    private BindCardRecord capitalBindApply(Loan loan, String orderNo, BairongDeductAgreementNoSyncRequest request) {
        UserBankCard userBankCard = userBankCardRepository.findById(loan.getLoanCardId()).orElseThrow();
        ExchangeCardApplyReq exchangeCardApplyReq = new ExchangeCardApplyReq();
        exchangeCardApplyReq.setLoanId(loan.getId());
        exchangeCardApplyReq.setPhone(request.getTelNo());//银行预留手机号
        exchangeCardApplyReq.setCardNo(request.getAcctNo());//扣款账户号
        exchangeCardApplyReq.setBoundSide(BoundSide.CAPITAL);//绑卡方
        exchangeCardApplyReq.setCardName(request.getAcctName());//扣款账户户名
        exchangeCardApplyReq.setAgreeNo(request.getSignNo());//支付通道扣款协议号
        exchangeCardApplyReq.setIdNo(userBankCard.getCertNo());//身份证
        CardBin cardBin = platformCardService.queryCardBin(request.getAcctNo());
        if (Objects.isNull(cardBin)) {
            //通过扣款账户号查询是否为支持的银行卡，不是。提示：不支持的银行卡
            logger.info("扣款账户号[" + request.getAcctNo() + "]不支持的银行卡");
            throw new BizException(ResultCode.CARD_NOT_SUPPORT);
        }
        String bankAbbr = cardBin.getBankAbbr();
        exchangeCardApplyReq.setBankCode(bankAbbr);
        exchangeCardApplyReq.setBankName(cardBin.getShortName());
        logger.info("调用资方换绑卡申请接口，进入换绑卡-申请方法的请求参数为:{}", JsonUtil.toJsonString(exchangeCardApplyReq));
        //调用资方换绑卡申请接口
        BindCardRecord bindCardRecord = capitalCardService.bindExchangeApply(loan, exchangeCardApplyReq);
        logger.info("调用资方换绑卡申请接口，完成处理后返回信息为:{}", JsonUtil.toJsonString(bindCardRecord));
        //组装百融换绑卡记录信息
        BairongRebindRecord rebindRecord = buildPpdRebindRecord(loan, bindCardRecord, orderNo);
        //初始化百融换绑卡记录信息
        bairongRebindRecordRepository.save(rebindRecord);
        return bindCardRecord;
    }

    /**
     * 组装百融换绑卡记录信息
     * @param loan           放款记录
     * @param bindCardRecord 绑卡记录信息
     * @param orderNo        外部订单号
     * @return 组装好的实体类
     */
    private BairongRebindRecord buildPpdRebindRecord(Loan loan, BindCardRecord bindCardRecord, String orderNo) {
        BairongRebindRecord rebindRecord = new BairongRebindRecord();
        rebindRecord.setCreditId(orderNo);
        rebindRecord.setUserId(loan.getUserId());
        rebindRecord.setLoanStage(LoanStage.REPAY.name());
        rebindRecord.setBindCardRecordId(bindCardRecord.getId());//绑卡记录id
        rebindRecord.setBoundSide(BoundSide.CAPITAL);
        ProcessState state = bindCardRecord.getState();//绑卡状态
        //已签约
        if (Objects.equals(state, ProcessState.SUCCEED)) {//成功
            rebindRecord.setState(ProcessState.SUCCEED);
            rebindRecord.setBindCardRecordId(loan.getRepayCardId());//绑卡记录id
        } else if (Objects.equals(state, ProcessState.FAILED)) {//失败
            rebindRecord.setState(ProcessState.FAILED);
        } else {//处理中
            rebindRecord.setState(ProcessState.PROCESSING);
        }
        bairongRebindRecordRepository.save(rebindRecord);
        return rebindRecord;
    }

}
