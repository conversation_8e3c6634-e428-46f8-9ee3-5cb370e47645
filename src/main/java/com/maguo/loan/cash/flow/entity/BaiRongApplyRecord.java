package com.maguo.loan.cash.flow.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "bairong_apply_record")
public class BaiRongApplyRecord extends BaseEntity {



    @Column(name = "order_no", length = 50, nullable = false)
    private String orderNo; // 百融订单编号

    private String idPositive; // 身份证头像面照
    private String idNegative; // 身份证国徽面照
    private String livePhoto; // 活体照片
    private String mobile; // 手机号
    private String name; // 姓名（OCR）
    private String email; // 邮箱
    private String idCardNo; // 身份证号（OCR）
    private String idStartTime; // 身份证有效期始
    private String idEndTime; // 身份证有效期止
    private String idAddress; // 身份证地址
    private String idSex; // 性别
    private String idEthnic; // 民族
    private String idIssueOrg; // 签发机关
    private String education; // 教育程度
    private String job; // 职业类别
    private String workUnitName; // 工作单位
    private String workUnitAddress; // 工作地址
    private String workUnitProvinceCode;
    private String workUnitCityCode;
    private String workUnitAreaCode;
    private String monthlyIncome; // 月收入
    private String faceScore; // 人脸分
    private String loanPurpose; // 借款用途
    private String latitude;
    private String longitude;
    private String relations; // 联系人
    private String deviceInfo; // 设备信息
    private String remark;
    private Integer revision; // 乐观锁
    private String createdBy;
    private LocalDateTime createdTime;
    private String updatedBy;
    private LocalDateTime updatedTime;
    private String facialSupplier;
    private String marriage; // 婚姻状况
    private String industry; // 行业
    private String isIncludingEquity; // 是否含权益
    private String equityRecipient; // 权益收取方
    private String livingAddress;
    private String livingProvinceCode;
    private String livingCityCode;
    private String livingAreaCode;
    private String livingProvince;
    private String livingCity;
    private String livingArea;
    private String imageId; //影像Id
    private String idPositiveOssKey;
    private String idNegativeOssKey;
    private String livePhotoOssKey;

    public String getImageId() {
        return imageId;
    }

    public void setImageId(String imageId) {
        this.imageId = imageId;
    }

    public String getIdPositiveOssKey() {
        return idPositiveOssKey;
    }

    public void setIdPositiveOssKey(String idPositiveOssKey) {
        this.idPositiveOssKey = idPositiveOssKey;
    }

    public String getIdNegativeOssKey() {
        return idNegativeOssKey;
    }

    public void setIdNegativeOssKey(String idNegativeOssKey) {
        this.idNegativeOssKey = idNegativeOssKey;
    }

    public String getLivePhotoOssKey() {
        return livePhotoOssKey;
    }

    public void setLivePhotoOssKey(String livePhotoOssKey) {
        this.livePhotoOssKey = livePhotoOssKey;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getIdPositive() {
        return idPositive;
    }

    public void setIdPositive(String idPositive) {
        this.idPositive = idPositive;
    }

    public String getIdNegative() {
        return idNegative;
    }

    public void setIdNegative(String idNegative) {
        this.idNegative = idNegative;
    }

    public String getLivePhoto() {
        return livePhoto;
    }

    public void setLivePhoto(String livePhoto) {
        this.livePhoto = livePhoto;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public String getIdStartTime() {
        return idStartTime;
    }

    public void setIdStartTime(String idStartTime) {
        this.idStartTime = idStartTime;
    }

    public String getIdEndTime() {
        return idEndTime;
    }

    public void setIdEndTime(String idEndTime) {
        this.idEndTime = idEndTime;
    }

    public String getIdAddress() {
        return idAddress;
    }

    public void setIdAddress(String idAddress) {
        this.idAddress = idAddress;
    }

    public String getIdSex() {
        return idSex;
    }

    public void setIdSex(String idSex) {
        this.idSex = idSex;
    }

    public String getIdEthnic() {
        return idEthnic;
    }

    public void setIdEthnic(String idEthnic) {
        this.idEthnic = idEthnic;
    }

    public String getIdIssueOrg() {
        return idIssueOrg;
    }

    public void setIdIssueOrg(String idIssueOrg) {
        this.idIssueOrg = idIssueOrg;
    }

    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public String getJob() {
        return job;
    }

    public void setJob(String job) {
        this.job = job;
    }

    public String getWorkUnitName() {
        return workUnitName;
    }

    public void setWorkUnitName(String workUnitName) {
        this.workUnitName = workUnitName;
    }

    public String getWorkUnitAddress() {
        return workUnitAddress;
    }

    public void setWorkUnitAddress(String workUnitAddress) {
        this.workUnitAddress = workUnitAddress;
    }

    public String getWorkUnitProvinceCode() {
        return workUnitProvinceCode;
    }

    public void setWorkUnitProvinceCode(String workUnitProvinceCode) {
        this.workUnitProvinceCode = workUnitProvinceCode;
    }

    public String getWorkUnitCityCode() {
        return workUnitCityCode;
    }

    public void setWorkUnitCityCode(String workUnitCityCode) {
        this.workUnitCityCode = workUnitCityCode;
    }

    public String getWorkUnitAreaCode() {
        return workUnitAreaCode;
    }

    public void setWorkUnitAreaCode(String workUnitAreaCode) {
        this.workUnitAreaCode = workUnitAreaCode;
    }

    public String getMonthlyIncome() {
        return monthlyIncome;
    }

    public void setMonthlyIncome(String monthlyIncome) {
        this.monthlyIncome = monthlyIncome;
    }

    public String getFaceScore() {
        return faceScore;
    }

    public void setFaceScore(String faceScore) {
        this.faceScore = faceScore;
    }

    public String getLoanPurpose() {
        return loanPurpose;
    }

    public void setLoanPurpose(String loanPurpose) {
        this.loanPurpose = loanPurpose;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getRelations() {
        return relations;
    }

    public void setRelations(String relations) {
        this.relations = relations;
    }

    public String getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public Integer getRevision() {
        return revision;
    }

    @Override
    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    @Override
    public String getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    @Override
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    @Override
    public String getUpdatedBy() {
        return updatedBy;
    }

    @Override
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Override
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    @Override
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getFacialSupplier() {
        return facialSupplier;
    }

    public void setFacialSupplier(String facialSupplier) {
        this.facialSupplier = facialSupplier;
    }

    public String getMarriage() {
        return marriage;
    }

    public void setMarriage(String marriage) {
        this.marriage = marriage;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public String getIsIncludingEquity() {
        return isIncludingEquity;
    }

    public void setIsIncludingEquity(String isIncludingEquity) {
        this.isIncludingEquity = isIncludingEquity;
    }

    public String getEquityRecipient() {
        return equityRecipient;
    }

    public void setEquityRecipient(String equityRecipient) {
        this.equityRecipient = equityRecipient;
    }

    public String getLivingAddress() {
        return livingAddress;
    }

    public void setLivingAddress(String livingAddress) {
        this.livingAddress = livingAddress;
    }

    public String getLivingProvinceCode() {
        return livingProvinceCode;
    }

    public void setLivingProvinceCode(String livingProvinceCode) {
        this.livingProvinceCode = livingProvinceCode;
    }

    public String getLivingCityCode() {
        return livingCityCode;
    }

    public void setLivingCityCode(String livingCityCode) {
        this.livingCityCode = livingCityCode;
    }

    public String getLivingAreaCode() {
        return livingAreaCode;
    }

    public void setLivingAreaCode(String livingAreaCode) {
        this.livingAreaCode = livingAreaCode;
    }

    public String getLivingProvince() {
        return livingProvince;
    }

    public void setLivingProvince(String livingProvince) {
        this.livingProvince = livingProvince;
    }

    public String getLivingCity() {
        return livingCity;
    }

    public void setLivingCity(String livingCity) {
        this.livingCity = livingCity;
    }

    public String getLivingArea() {
        return livingArea;
    }

    public void setLivingArea(String livingArea) {
        this.livingArea = livingArea;
    }
}

