package com.maguo.loan.cash.flow.entrance.bairong.dto.loan;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description 百融放款查询——请求参数
 * @Date 2025/9/12 14:30
 * @Version v1.0
 **/
public class BairongLoanQueryRequest {

    /**
     * 渠道放款流水号
     */
    @NotBlank(message = "渠道放款流水号不能为空")
    private String outLoanSeq;
    /**
     * 资金方授信流水号
     */
    private String applCde;
    /**
     * 资金方放款流水号
     */
    private String loanSeq;

    public String getOutLoanSeq() {
        return outLoanSeq;
    }

    public void setOutLoanSeq(String outLoanSeq) {
        this.outLoanSeq = outLoanSeq;
    }

    public String getApplCde() {
        return applCde;
    }

    public void setApplCde(String applCde) {
        this.applCde = applCde;
    }

    public String getLoanSeq() {
        return loanSeq;
    }

    public void setLoanSeq(String loanSeq) {
        this.loanSeq = loanSeq;
    }
}
