package com.maguo.loan.cash.flow.entrance.bairong.dto.callBack;

import lombok.Data;

/**
 * 权益订单通知 - 多次退款信息子实体类
 * 备注：用于描述订单的单笔退款详情（多笔退款时每笔对应一个该实体）
 */
@Data
public class RefundInfo {

    /**
     * 退款单号
     * 是否必填：N（否）
     * 备注：订单存在多次退款时必传，唯一标识单笔退款记录
     */
    private String refundNo;

    /**
     * 退款时间
     * 是否必填：N（否）
     * 备注：毫秒级时间戳，标识该笔退款的执行时间
     */
    private Long refundTime;

    /**
     * 退款金额
     * 是否必填：N（否）
     * 备注：订单存在多次退款时必传，该笔退款的金额（单位：元）
     */
    private Double refundAmount;

    /**
     * 退款途径
     * 是否必填：N（否）
     * 备注：可选值：
     * 0 - 线上退款
     * 1 - 线下退款
     */
    private Integer refundMethod;
}
