package com.maguo.loan.cash.flow.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.util.List;

public class BairongRepayCallbackRequest {
    @JsonProperty("outBatchRepaymentSeq")
    private String outBatchRepaymentSeq;

    @JsonProperty("setlSeq")
    private String setlSeq;

    @JsonProperty("loanNo")
    private String loanNo;

    @JsonProperty("billStatus")
    private String billStatus;

    @JsonProperty("failReason")
    private String failReason;

    @JsonProperty("applyRepayAmt")
    private BigDecimal applyRepayAmt;

    @JsonProperty("crtDt")
    private String crtDt;

    @JsonProperty("ppErInd")
    private String ppErInd;

    @JsonProperty("RepaymentList")
    private List<RepaymentDetailDto> repaymentList;

    public BairongRepayCallbackRequest() {
    }

    public String getOutBatchRepaymentSeq() {
        return outBatchRepaymentSeq;
    }

    public void setOutBatchRepaymentSeq(String outBatchRepaymentSeq) {
        this.outBatchRepaymentSeq = outBatchRepaymentSeq;
    }

    public String getSetlSeq() {
        return setlSeq;
    }

    public void setSetlSeq(String setlSeq) {
        this.setlSeq = setlSeq;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getBillStatus() {
        return billStatus;
    }

    public void setBillStatus(String billStatus) {
        this.billStatus = billStatus;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public BigDecimal getApplyRepayAmt() {
        return applyRepayAmt;
    }

    public void setApplyRepayAmt(BigDecimal applyRepayAmt) {
        this.applyRepayAmt = applyRepayAmt;
    }

    public String getCrtDt() {
        return crtDt;
    }

    public void setCrtDt(String crtDt) {
        this.crtDt = crtDt;
    }

    public String getPpErInd() {
        return ppErInd;
    }

    public void setPpErInd(String ppErInd) {
        this.ppErInd = ppErInd;
    }

    public List<RepaymentDetailDto> getRepaymentList() {
        return repaymentList;
    }

    public void setRepaymentList(List<RepaymentDetailDto> repaymentList) {
        this.repaymentList = repaymentList;
    }
}
