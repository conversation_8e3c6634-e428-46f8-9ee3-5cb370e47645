package com.maguo.loan.cash.flow.util;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

public class IdCardUtil {
    /**
     * 通过身份证号码获取出生日期。
     * <p>
     * 该方法支持18位和15位身份证号码。
     * 对于15位身份证号码，年份会默认加上"19"。
     *
     * @param idCard 身份证号码
     * @return 出生日期 (LocalDate对象)
     * @throws IllegalArgumentException 如果身份证号码为null、格式无效或其中包含的日期不合法
     */
    public static LocalDate getBirthDate(String idCard) {
        // 1. 基础校验
        if (idCard == null || idCard.trim().isEmpty()) {
            throw new IllegalArgumentException("身份证号码不能为空");
        }

        String birthDateStr;

        // 2. 根据身份证长度截取日期字符串
        if (idCard.length() == 18) {
            birthDateStr = idCard.substring(6, 14);
        } else if (idCard.length() == 15) {
            // 15位身份证的年份是两位数，前面需要补上 "19"
            birthDateStr = "19" + idCard.substring(6, 12);
        } else {
            throw new IllegalArgumentException("无效的身份证号码长度，应为15位或18位");
        }

        // 3. 将日期字符串解析为 LocalDate 对象
        try {
            // 使用DateTimeFormatter进行解析，可以自动校验日期的有效性（如2月30日）
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            return LocalDate.parse(birthDateStr, formatter);
        } catch (DateTimeParseException e) {
            // 如果日期格式不正确（例如 "20230230"），则抛出异常
            throw new IllegalArgumentException("身份证号码中包含无效的出生日期", e);
        }
    }


    /**
     * 通过身份证号码获取出生日期字符串 (默认格式 "yyyy-MM-dd")。
     *
     * @param idCard 身份证号码
     * @return 格式为 "yyyy-MM-dd" 的出生日期字符串
     * @throws IllegalArgumentException 如果身份证号码无效
     */
    public static String getBirthDateAsString(String idCard) {
        // 直接调用下面的方法，并传入一个默认的格式
        return getBirthDateAsString(idCard, "yyyy-MM-dd");
    }

    /**
     * 通过身份证号码获取自定义格式的出生日期字符串。
     *
     * @param idCard  身份证号码
     * @param pattern 自定义的日期格式，例如 "yyyy年MM月dd日"
     * @return 格式化后的出生日期字符串
     * @throws IllegalArgumentException 如果身份证号码无效或日期格式模板不正确
     */
    public static String getBirthDateAsString(String idCard, String pattern) {
        // 1. 复用之前的方法获取LocalDate对象，包含了所有校验逻辑
        LocalDate birthDate = getBirthDate(idCard);

        if (pattern == null || pattern.trim().isEmpty()) {
            throw new IllegalArgumentException("日期格式模板不能为空");
        }

        // 2. 根据传入的格式，将LocalDate对象格式化为字符串
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
            return birthDate.format(formatter);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("无效的日期格式模板: " + pattern, e);
        }
    }
}
