package com.maguo.loan.cash.flow.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;


public class RepaymentDetailDto {
    @JsonProperty("perdNo")
    private Integer perdNo;

    @JsonProperty("applyRePayAmt")
    private BigDecimal applyRePayAmt;

    @JsonProperty("prinAmt")
    private BigDecimal prinAmt;

    @JsonProperty("intAmt")
    private BigDecimal intAmt;

    @JsonProperty("odIntAmt")
    private BigDecimal odIntAmt;

    @JsonProperty("commIntAmt")
    private BigDecimal commIntAmt;

    @JsonProperty("feeAmt")
    private BigDecimal feeAmt;

    @JsonProperty("grtAmt")
    private BigDecimal grtAmt;

    @JsonProperty("penlAmt")
    private BigDecimal penlAmt;

    @JsonProperty("otherAmt")
    private BigDecimal otherAmt;

    @JsonProperty("deductAmt")
    private BigDecimal deductAmt;

    public RepaymentDetailDto() {
    }

    public Integer getPerdNo() {
        return perdNo;
    }

    public void setPerdNo(Integer perdNo) {
        this.perdNo = perdNo;
    }

    public BigDecimal getApplyRePayAmt() {
        return applyRePayAmt;
    }

    public void setApplyRePayAmt(BigDecimal applyRePayAmt) {
        this.applyRePayAmt = applyRePayAmt;
    }

    public BigDecimal getPrinAmt() {
        return prinAmt;
    }

    public void setPrinAmt(BigDecimal prinAmt) {
        this.prinAmt = prinAmt;
    }

    public BigDecimal getIntAmt() {
        return intAmt;
    }

    public void setIntAmt(BigDecimal intAmt) {
        this.intAmt = intAmt;
    }

    public BigDecimal getOdIntAmt() {
        return odIntAmt;
    }

    public void setOdIntAmt(BigDecimal odIntAmt) {
        this.odIntAmt = odIntAmt;
    }

    public BigDecimal getCommIntAmt() {
        return commIntAmt;
    }

    public void setCommIntAmt(BigDecimal commIntAmt) {
        this.commIntAmt = commIntAmt;
    }

    public BigDecimal getFeeAmt() {
        return feeAmt;
    }

    public void setFeeAmt(BigDecimal feeAmt) {
        this.feeAmt = feeAmt;
    }

    public BigDecimal getGrtAmt() {
        return grtAmt;
    }

    public void setGrtAmt(BigDecimal grtAmt) {
        this.grtAmt = grtAmt;
    }

    public BigDecimal getPenlAmt() {
        return penlAmt;
    }

    public void setPenlAmt(BigDecimal penlAmt) {
        this.penlAmt = penlAmt;
    }

    public BigDecimal getOtherAmt() {
        return otherAmt;
    }

    public void setOtherAmt(BigDecimal otherAmt) {
        this.otherAmt = otherAmt;
    }

    public BigDecimal getDeductAmt() {
        return deductAmt;
    }

    public void setDeductAmt(BigDecimal deductAmt) {
        this.deductAmt = deductAmt;
    }


    public static RepaymentDetailDto from(CustomRepayRecord record) {
        RepaymentDetailDto dto = new RepaymentDetailDto();

        dto.setPerdNo(record.getPeriod());
        dto.setApplyRePayAmt(record.getTotalAmt());
        dto.setPrinAmt(record.getPrincipalAmt());
        dto.setIntAmt(record.getInterestAmt());
        dto.setOdIntAmt(record.getPenaltyAmt());
        dto.setGrtAmt(record.getGuaranteeAmt());
        dto.setPenlAmt(record.getBreachAmt());
        dto.setDeductAmt(record.getReduceAmount());


        dto.setCommIntAmt(BigDecimal.ZERO);
        dto.setFeeAmt(BigDecimal.ZERO);
        dto.setOtherAmt(BigDecimal.ZERO);

        return dto;
    }
}
