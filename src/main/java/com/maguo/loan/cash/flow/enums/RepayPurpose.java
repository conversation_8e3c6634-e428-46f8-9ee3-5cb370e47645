package com.maguo.loan.cash.flow.enums;

/**
 * <AUTHOR>
 * @date 2023/9/22
 */
public enum RepayPurpose {
    /**
     * 还当期
     */
    CURRENT,
    /**
     * 结清
     */
    CLEAR;



    public static RepayPurpose toRepayType(String relation) {
        return switch (relation) {
            case "1" -> CURRENT;
            case "2" -> CLEAR;
            default -> null;
        };
    }

    public static RepayPurpose toFqlRepayType(String relation) {
        return switch (relation) {
            case "1", "3" -> CURRENT;
            case "4" -> CLEAR;
            default -> null;
        };
    }

    public static RepayPurpose toFqlRepayNotifyType(String relation) {
        return switch (relation) {
            case "10", "40" -> CURRENT;
            case "30" -> <PERSON><PERSON>AR;
            default -> null;
        };
    }

    public static RepayPurpose toPpdRepayType(String relation) {
        return switch (relation) {
            case "01","04" -> CURRENT;
            case "03" -> CLEAR;
            default -> null;
        };
    }

    public static RepayPurpose toFenQiLeRepayType(String relation) {
        return switch (relation) {
            case "10" -> CURRENT;
            case "30" -> CLEAR;
            default -> null;
        };
    }

    public static RepayPurpose toBairongRepayType(String relation) {
        return switch (relation) {
            case "0","1" -> CURRENT;
            case "2" -> CLEAR;
            default -> null;
        };
    }

}
