package com.maguo.loan.cash.flow.entrance.bairong.controller;

import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongDeductAgreementNoSyncRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongDeductAgreementNoSyncResponse;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongRepayRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongRepayResponse;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongRepayResultRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongRepayResultResponse;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongRepayTrialRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongRepayTrialResponse;
import com.maguo.loan.cash.flow.entrance.bairong.exception.BairongException;
import com.maguo.loan.cash.flow.entrance.bairong.service.BairongRepayService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/9/11 11:22
 **/
@RestController
@RequestMapping("/bairong")
public class BairongRepayController extends BairongApiValidator {

    @Autowired
    private BairongRepayService baiRongRepayService;

    /**
     * 还款试算
     */
    @RequestMapping("/repayment/trial/BRYC")
    public BairongRepayTrialResponse repayTrial(@RequestBody @Valid BairongRepayTrialRequest request, BindingResult bindingResult) throws BairongException {
        //必填参数校验
        validate(bindingResult);
        //业务逻辑
        return baiRongRepayService.trail(request);
    }

    /**
     * 还款申请
     */
    @RequestMapping("/repayment/apply/BRYC")
    public BairongRepayResponse repayApply(@RequestBody @Valid BairongRepayRequest request, BindingResult bindingResult) throws BairongException {
        // 必填参数校验
        validate(bindingResult);
        // 业务逻辑
        return baiRongRepayService.repay(request);
    }

    /**
     * 还款结果查询
     */
    @RequestMapping("/repayment/query/BRYC")
    public BairongRepayResultResponse repayQuery(@RequestBody @Valid BairongRepayResultRequest request, BindingResult bindingResult) throws BairongException {
        // 必填参数校验
        validate(bindingResult);
        // 业务逻辑
        return baiRongRepayService.repayQuery(request);
    }

    /**
     * 还款协议号同步
     */
    @RequestMapping("/protocol/sync/BRYC")
    public BairongDeductAgreementNoSyncResponse repayAgreementNoSync(@RequestBody @Valid BairongDeductAgreementNoSyncRequest request, BindingResult bindingResult) throws BairongException {
        // 必填参数校验
        validate(bindingResult);
        // 业务逻辑
        return baiRongRepayService.repayAgreementNoSync(request);
    }

}
