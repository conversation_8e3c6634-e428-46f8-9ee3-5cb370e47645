package com.maguo.loan.cash.flow.repository.bairong;

import com.maguo.loan.cash.flow.entity.bairong.BairongAgreementUploadRecord;
import com.maguo.loan.cash.flow.enums.ProcessState;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/9/16 10:40
 */
public interface BairongAgreementUploadRecordRepository extends JpaRepository<BairongAgreementUploadRecord, String> {


    /**
     * 根据多个条件查询上传记录
     *
     * @param outApplSeq    渠道授信流水号（可为空）
     * @param outLoanSeq    渠道放款流水号（可为空）
     * @param businessStage 业务阶段（可为空，1-授信阶段 2-放款阶段 3-绑卡阶段 4-全部）
     * @param uploadStatus  上传状态（可为空）
     * @return 上传记录列表
     */
    @Query("SELECT r FROM BairongAgreementUploadRecord r WHERE " +
        "(:outApplSeq IS NULL OR r.outApplSeq = :outApplSeq) AND " +
        "(:outLoanSeq IS NULL OR r.outLoanSeq = :outLoanSeq) AND " +
        "(:businessStage IS NULL OR :businessStage = '4' OR r.businessStage = :businessStage) AND " +
        "(:uploadStatus IS NULL OR r.uploadStatus = :uploadStatus)")
    List<BairongAgreementUploadRecord> findByConditions(@Param("outApplSeq") String outApplSeq,
                                                        @Param("outLoanSeq") String outLoanSeq,
                                                        @Param("businessStage") String businessStage,
                                                        @Param("uploadStatus") ProcessState uploadStatus);

    /**
     * 根据借据ID查询上传记录
     *
     * @param loanId 借据ID
     * @return 上传记录列表
     */
    List<BairongAgreementUploadRecord> findByLoanId(String loanId);
}
