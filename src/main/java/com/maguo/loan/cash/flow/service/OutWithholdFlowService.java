package com.maguo.loan.cash.flow.service;

import com.maguo.loan.cash.flow.entity.OutWithholdFlow;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.repository.OutWithholdFlowRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @title:
 * @desc:
 * @date 2025/9/15 15:41
 */
@Service
public class OutWithholdFlowService {

    @Autowired
    private OutWithholdFlowRepository outWithholdFlowRepository;

    public void save (OutWithholdFlow outWithholdFlow) {
        outWithholdFlowRepository.save(outWithholdFlow);
    }

    public Optional<OutWithholdFlow> findByLoanIdAndPeriodAndPayState (String loanId, Integer period, ProcessState payState) {
        return outWithholdFlowRepository.findByLoanIdAndPeriodAndPayState( loanId,  period, payState);
    }

    public void delete (OutWithholdFlow outWithholdFlow) {
        outWithholdFlowRepository.delete(outWithholdFlow);
    }
}
