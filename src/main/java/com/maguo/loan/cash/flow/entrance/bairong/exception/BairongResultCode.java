package com.maguo.loan.cash.flow.entrance.bairong.exception;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 百融返回码枚举
 * @date 2025/9/11 9:19
 */
public enum BairongResultCode {
    SUCCESS("0000", "请求成功"),
    FAILURE("0001", "内部错误，接口调用失败"),
    INVALID_PARAM("0003", "参数错误"),
    SIGN_VERIFY_FAIL("0004", "签名错误"),
    DECRYPT_FAIL("0005", "解密错误"),
    ENCRYPT_FAIL("0006", "加密错误"),
    UNKNOWN_ERROR("9999", "未知错误"),
    OUT_LOAN_SEQ_CAN_NOT_BE_NULL("0011", "[outLoanSeq]不能为空"),
    ORDER_ALREADY_REJECT("0012", "订单已拒绝"),
    ALREADY_AN_ORDER_IN_TRANSIT("0013", "存在在途订单"),
    OVERALL_SCORE_IS_INSUFFICIENT("0014", "综合评分不足"),
    SYSTEM_ERROR("0015", "标识失败"),
    LOAN_NOT_EXIST("0016", "借款记录不存在"),
    REPAYMENT_TYPE_UNSUPPORTED("0017", "不支持该还款类型"),
    REPAY_PLAN_NO_NORMAL_ERROR("0018", "该笔借据下的还款计划没有待还的记录"),
    LOAN_NOT_EXIST_OR_ORDER_NOT_CLEAR("0019", "借据不存在或订单未结清"),
    CLEAR_ALREADY_EXIST("0020", "结清证明已存在"),
    CLEAR_APPLY_FAIL("0021", "下载结清证明申请失败"),
    CLEAR_PROCESSING("0022", "处理中"),
    BAIRONG_TRIAL_PERIOD_ERROR("0023", "百融还款试算失败，本期已还款成功"),
    BAIRONG_REPAY_PERIOD_ERROR("0024", "百融还款申请失败，本期已还款成功"),
    ACC_INFO_LIST_IS_NULL_ERROR("0025", "百融还款申请时，账号信息不能为空！"),
    REPAY_INFO_LIST_IS_NULL_ERROR("0026", "百融还款申请时，账单信息不能为空！"),
    REPAY_NOT_SUPPORTED_LOAN_DATE("0027", "放款日当天不允许发起还款"),
    NO_SUBMIT_REPEAT("0028", "百融-乐通分期还款协议号同步，请勿重复提交。"),
    OUTLOANSEQ_IS_NULL_ERROR("0029", "还款协议号同步时，outLoanSeq（渠道放款流水号）不能为空"),
    CREDIT_INFO_IS_NULL_ERROR("0030", "还款协议号同步时，通过资金方授信流水号查询授信记录为空"),
    REPAY_IS_PENDING_ERROR("0031", "借据下有在途的还款订单，请等该还款返回终态后重试"),
    ORDER_INFO_IS_NULL_ERROR("0032", "还款协议号同步时，订单信息不能为空！"),
    LOAN_RETURN_MESSAGE_DESCRIPTION_CREDIT_INFO("0033", "无有效授信，不可发起借款"),
    LOAN_RETURN_MESSAGE_DESCRIPTION_PARAM("0034", "放款申请参数校验失败"),
    LOAN_RETURN_MESSAGE_DESCRIPTION_BANK("0035", "不支持的银行卡"),
    OSS_UPLOAD_FAIL("0036", "阿里云OSS上传失败"),
    IMAGE_UPLOAD_FAIL("0037", "影像件上传失败"),
    REMARK_MSG_INFO("0038", "头寸不足"),
    REMARK_MSG_AMOUNT_INFO("0039", "授信放款金额不一致"),
    ACCT_NO_CHECK_ERROR("0101", "存在贷后换绑卡情况，银行卡账号不一致。请先同步协议号后再进行还款！"),
    EQUITY_ORDER_NOTIFY_FAIL("2003", "权益订单通知业务处理失败"),
    EQUITY_ORDER_NOTIFY_SYSTEM_ERROR("2004", "系统异常"),

    OUTAPPLSEQ_OR_OUTLOANSEQ_IS_NULL_ERROR("1001", "outApplSeq和outLoanSeq两者至少必传一个"),

    ;

    private String code;
    private String msg;

    BairongResultCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public BairongResultCode getByCode(String code) {
        for (BairongResultCode resultCode : BairongResultCode.values()) {
            if (Objects.equals(resultCode.getCode(), code)) {
                return resultCode;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
