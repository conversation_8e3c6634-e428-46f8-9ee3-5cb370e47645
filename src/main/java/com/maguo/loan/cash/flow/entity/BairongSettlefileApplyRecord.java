package com.maguo.loan.cash.flow.entity;


import jakarta.persistence.Entity;
import jakarta.persistence.Table;

/**
 * <p>
 * 百融结清证明记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-12
 */
@Entity
@Table(name = "bairong_settlefile_apply_record")
public class BairongSettlefileApplyRecord extends BaseEntity {

    /**
     * 借据id
     */
    private String loanId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件内容
     */
    private String fileContent;

    /**
     * 状态
     */
    private String status;

    /**
     * 失败原因
     */
    private String failReason;


    @Override
    protected String prefix() {
        return "BSAR";
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileContent() {
        return fileContent;
    }

    public void setFileContent(String fileContent) {
        this.fileContent = fileContent;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }
}
