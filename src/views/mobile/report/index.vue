<template>
  <div class="mobile-report">
    <!-- 数据概览卡片 -->
    <div class="overview-cards">
      <div class="card-row">
        <div class="overview-card">
          <div class="card-value">{{ formatNumber(overviewData.todayRegister) }}</div>
          <div class="card-label">今日注册</div>
          <div class="card-trend up">
            <i class="el-icon-top"></i>
            +12%
          </div>
        </div>
        <div class="overview-card">
          <div class="card-value">{{ formatNumber(overviewData.todayLoan) }}</div>
          <div class="card-label">今日放款</div>
          <div class="card-trend down">
            <i class="el-icon-bottom"></i>
            -5%
          </div>
        </div>
      </div>
      <div class="card-row">
        <div class="overview-card">
          <div class="card-value">{{ formatMoney(overviewData.todayAmount) }}</div>
          <div class="card-label">今日金额(万)</div>
          <div class="card-trend up">
            <i class="el-icon-top"></i>
            +8%
          </div>
        </div>
        <div class="overview-card">
          <div class="card-value">{{ overviewData.passRate }}%</div>
          <div class="card-label">通过率</div>
          <div class="card-trend up">
            <i class="el-icon-top"></i>
            +2%
          </div>
        </div>
      </div>
    </div>

    <!-- 时间筛选 -->
    <div class="filter-section">
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        size="small"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        @change="handleDateChange"
      />
      <el-button size="small" type="primary" @click="refreshData">刷新</el-button>
    </div>

    <!-- 图表区域 -->
    <div class="chart-section">
      <!-- 趋势图 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>业务趋势</h3>
          <el-radio-group v-model="trendType" size="mini" @change="updateTrendChart">
            <el-radio-button label="register">注册</el-radio-button>
            <el-radio-button label="loan">放款</el-radio-button>
            <el-radio-button label="amount">金额</el-radio-button>
          </el-radio-group>
        </div>
        <div class="chart-container">
          <div ref="trendChart" class="chart"></div>
        </div>
      </div>

      <!-- 饼图 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>渠道分布</h3>
        </div>
        <div class="chart-container">
          <div ref="pieChart" class="chart"></div>
        </div>
      </div>
    </div>

    <!-- 数据列表 -->
    <div class="data-section">
      <div class="section-header">
        <h3>详细数据</h3>
        <el-button size="mini" type="text" @click="exportData">导出</el-button>
      </div>
      <div class="data-table">
        <div class="table-header">
          <div class="col">日期</div>
          <div class="col">注册</div>
          <div class="col">放款</div>
          <div class="col">金额(万)</div>
        </div>
        <div class="table-body">
          <div class="table-row" v-for="item in tableData" :key="item.date">
            <div class="col">{{ item.date }}</div>
            <div class="col">{{ item.register }}</div>
            <div class="col">{{ item.loan }}</div>
            <div class="col">{{ formatMoney(item.amount) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { queryRecord } from '@/api/statistic'

export default {
  name: 'MobileReport',
  data() {
    return {
      dateRange: [],
      trendType: 'register',
      trendChart: null,
      pieChart: null,
      overviewData: {
        todayRegister: 1234,
        todayLoan: 567,
        todayAmount: 890.5,
        passRate: 78.5
      },
      tableData: []
    }
  },
  mounted() {
    this.initDateRange()
    this.initCharts()
    this.loadData()
  },
  beforeDestroy() {
    if (this.trendChart) {
      this.trendChart.dispose()
    }
    if (this.pieChart) {
      this.pieChart.dispose()
    }
  },
  methods: {
    initDateRange() {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      this.dateRange = [this.formatDate(start), this.formatDate(end)]
    },

    initCharts() {
      this.$nextTick(() => {
        this.initTrendChart()
        this.initPieChart()
      })
    },

    initTrendChart() {
      this.trendChart = echarts.init(this.$refs.trendChart)
      const option = {
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          data: [120, 200, 150, 80, 70, 110, 130],
          type: 'line',
          smooth: true,
          itemStyle: {
            color: '#1A7EFD'
          }
        }]
      }
      this.trendChart.setOption(option)
    },

    initPieChart() {
      this.pieChart = echarts.init(this.$refs.pieChart)
      const option = {
        series: [{
          name: '渠道分布',
          type: 'pie',
          radius: '60%',
          data: [
            { value: 335, name: '直接访问' },
            { value: 310, name: '邮件营销' },
            { value: 234, name: '联盟广告' },
            { value: 135, name: '视频广告' },
            { value: 1548, name: '搜索引擎' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }
      this.pieChart.setOption(option)
    },

    updateTrendChart() {
      // 根据选择的类型更新趋势图数据
      const mockData = {
        register: [120, 200, 150, 80, 70, 110, 130],
        loan: [80, 120, 100, 60, 50, 80, 90],
        amount: [200, 300, 250, 150, 120, 180, 220]
      }
      
      this.trendChart.setOption({
        series: [{
          data: mockData[this.trendType]
        }]
      })
    },

    async loadData() {
      try {
        // 这里调用实际的API接口
        // const response = await queryRecord({
        //   startDate: this.dateRange[0],
        //   endDate: this.dateRange[1]
        // })
        
        // 模拟数据
        this.tableData = [
          { date: '2024-01-01', register: 100, loan: 50, amount: 125.5 },
          { date: '2024-01-02', register: 120, loan: 60, amount: 150.0 },
          { date: '2024-01-03', register: 90, loan: 45, amount: 112.5 },
          { date: '2024-01-04', register: 110, loan: 55, amount: 137.5 },
          { date: '2024-01-05', register: 130, loan: 65, amount: 162.5 }
        ]
      } catch (error) {
        this.$message.error('数据加载失败')
        console.error('加载数据失败:', error)
      }
    },

    handleDateChange() {
      this.loadData()
    },

    refreshData() {
      this.loadData()
      this.$message.success('数据已刷新')
    },

    exportData() {
      this.$message.info('导出功能开发中...')
    },

    formatNumber(num) {
      return num.toLocaleString()
    },

    formatMoney(amount) {
      return (amount / 10000).toFixed(1)
    },

    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    }
  }
}
</script>

<style lang="scss" scoped>
.mobile-report {
  padding: 15px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 110px);
}

.overview-cards {
  margin-bottom: 15px;

  .card-row {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .overview-card {
    flex: 1;
    background: #fff;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;

    .card-value {
      font-size: 24px;
      font-weight: bold;
      color: #333;
      margin-bottom: 5px;
    }

    .card-label {
      font-size: 12px;
      color: #666;
      margin-bottom: 8px;
    }

    .card-trend {
      font-size: 12px;
      position: absolute;
      top: 15px;
      right: 15px;

      &.up {
        color: #67c23a;
      }

      &.down {
        color: #f56c6c;
      }

      i {
        margin-right: 2px;
      }
    }
  }
}

.filter-section {
  background: #fff;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;

  .el-date-editor {
    flex: 1;
  }
}

.chart-section {
  .chart-card {
    background: #fff;
    border-radius: 8px;
    margin-bottom: 15px;
    overflow: hidden;

    .chart-header {
      padding: 15px;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        font-size: 16px;
        color: #333;
      }
    }

    .chart-container {
      padding: 15px;

      .chart {
        width: 100%;
        height: 200px;
      }
    }
  }
}

.data-section {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;

  .section-header {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 16px;
      color: #333;
    }
  }

  .data-table {
    .table-header,
    .table-row {
      display: flex;
      padding: 0 15px;

      .col {
        flex: 1;
        padding: 12px 5px;
        text-align: center;
        font-size: 14px;
      }
    }

    .table-header {
      background: #f8f9fa;
      font-weight: bold;
      color: #333;
      border-bottom: 1px solid #e9ecef;
    }

    .table-row {
      border-bottom: 1px solid #f0f0f0;
      color: #666;

      &:last-child {
        border-bottom: none;
      }

      &:nth-child(even) {
        background: #fafafa;
      }
    }
  }
}
</style>
