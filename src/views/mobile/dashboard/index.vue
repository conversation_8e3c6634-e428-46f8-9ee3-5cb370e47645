<template>
  <div class="mobile-dashboard">
    <!-- 用户信息卡片 -->
    <div class="user-card">
      <div class="user-info">
        <img :src="userAvatar" alt="头像" class="avatar" />
        <div class="info">
          <div class="name">{{ userName }}</div>
          <div class="role">{{ userRole }}</div>
        </div>
      </div>
      <div class="weather">
        <i class="el-icon-sunny"></i>
        <span>晴 25°C</span>
      </div>
    </div>

    <!-- 快捷功能 -->
    <div class="quick-actions">
      <div class="action-item" @click="goToReport">
        <div class="icon-wrapper">
          <i class="el-icon-s-data"></i>
        </div>
        <span>数据报表</span>
      </div>
      <div class="action-item" @click="goToApproval">
        <div class="icon-wrapper">
          <i class="el-icon-document-checked"></i>
        </div>
        <span>审批流程</span>
      </div>
      <div class="action-item" @click="goToMonitor">
        <div class="icon-wrapper">
          <i class="el-icon-monitor"></i>
        </div>
        <span>系统监控</span>
      </div>
      <div class="action-item" @click="goToSettings">
        <div class="icon-wrapper">
          <i class="el-icon-setting"></i>
        </div>
        <span>系统设置</span>
      </div>
    </div>

    <!-- 今日数据概览 -->
    <div class="today-overview">
      <div class="section-title">
        <h3>今日概览</h3>
        <span class="update-time">更新时间: {{ updateTime }}</span>
      </div>
      <div class="overview-grid">
        <div class="overview-item">
          <div class="value">{{ todayData.register }}</div>
          <div class="label">新增注册</div>
          <div class="change positive">+{{ todayData.registerChange }}%</div>
        </div>
        <div class="overview-item">
          <div class="value">{{ todayData.apply }}</div>
          <div class="label">申请笔数</div>
          <div class="change positive">+{{ todayData.applyChange }}%</div>
        </div>
        <div class="overview-item">
          <div class="value">{{ todayData.loan }}</div>
          <div class="label">放款笔数</div>
          <div class="change negative">{{ todayData.loanChange }}%</div>
        </div>
        <div class="overview-item">
          <div class="value">{{ todayData.amount }}万</div>
          <div class="label">放款金额</div>
          <div class="change positive">+{{ todayData.amountChange }}%</div>
        </div>
      </div>
    </div>

    <!-- 趋势图表 -->
    <div class="trend-chart">
      <div class="section-title">
        <h3>7日趋势</h3>
        <el-radio-group v-model="chartType" size="mini" @change="updateChart">
          <el-radio-button label="register">注册</el-radio-button>
          <el-radio-button label="loan">放款</el-radio-button>
        </el-radio-group>
      </div>
      <div class="chart-container">
        <div ref="trendChart" class="chart"></div>
      </div>
    </div>

    <!-- 系统公告 -->
    <div class="notice-section">
      <div class="section-title">
        <h3>系统公告</h3>
        <el-button size="mini" type="text" @click="viewAllNotices">更多</el-button>
      </div>
      <div class="notice-list">
        <div class="notice-item" v-for="item in noticeList" :key="item.id" @click="viewNotice(item)">
          <div class="notice-title">{{ item.title }}</div>
          <div class="notice-time">{{ item.time }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import Avatar from '@/assets/images/avatar.png'

export default {
  name: 'MobileDashboard',
  data() {
    return {
      userAvatar: Avatar,
      userName: '管理员',
      userRole: '系统管理员',
      updateTime: '',
      chartType: 'register',
      trendChart: null,
      todayData: {
        register: 1234,
        registerChange: 12.5,
        apply: 567,
        applyChange: 8.3,
        loan: 234,
        loanChange: -2.1,
        amount: 456.7,
        amountChange: 15.2
      },
      todoList: [
        {
          id: 1,
          title: '审批待处理订单',
          description: '有5笔订单等待审批',
          time: '2小时前',
          priority: 'high',
          priorityText: '紧急'
        },
        {
          id: 2,
          title: '系统维护通知',
          description: '今晚22:00-24:00系统维护',
          time: '4小时前',
          priority: 'medium',
          priorityText: '一般'
        }
      ],
      noticeList: [
        {
          id: 1,
          title: '系统升级公告',
          time: '2024-01-15'
        },
        {
          id: 2,
          title: '新功能上线通知',
          time: '2024-01-12'
        }
      ]
    }
  },
  mounted() {
    this.updateTime = this.getCurrentTime()
    this.initChart()
    this.loadDashboardData()
  },
  beforeDestroy() {
    if (this.trendChart) {
      this.trendChart.dispose()
    }
  },
  methods: {
    initChart() {
      this.$nextTick(() => {
        this.trendChart = echarts.init(this.$refs.trendChart)
        this.updateChart()
      })
    },

    updateChart() {
      const mockData = {
        register: [120, 200, 150, 80, 70, 110, 130],
        loan: [80, 120, 100, 60, 50, 80, 90]
      }

      const option = {
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['1/9', '1/10', '1/11', '1/12', '1/13', '1/14', '1/15']
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          data: mockData[this.chartType],
          type: 'line',
          smooth: true,
          itemStyle: {
            color: '#1A7EFD'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: 'rgba(26, 126, 253, 0.3)'
              }, {
                offset: 1, color: 'rgba(26, 126, 253, 0.1)'
              }]
            }
          }
        }]
      }
      this.trendChart.setOption(option)
    },

    loadDashboardData() {
      // 这里可以调用API加载实际数据
      console.log('加载仪表板数据')
    },

    getCurrentTime() {
      const now = new Date()
      return now.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    goToReport() {
      this.$router.push('/mobile/report')
    },

    goToApproval() {
      this.$message.info('审批功能开发中...')
    },

    goToMonitor() {
      this.$message.info('监控功能开发中...')
    },

    goToSettings() {
      this.$message.info('设置功能开发中...')
    },

    viewAllTodos() {
      this.$message.info('待办列表功能开发中...')
    },

    handleTodo(item) {
      this.$message.info(`处理待办: ${item.title}`)
    },

    viewAllNotices() {
      this.$message.info('公告列表功能开发中...')
    },

    viewNotice(item) {
      this.$message.info(`查看公告: ${item.title}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.mobile-dashboard {
  padding: 15px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 110px);
}

.user-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .user-info {
    display: flex;
    align-items: center;

    .avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      margin-right: 15px;
    }

    .info {
      .name {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
      }

      .role {
        font-size: 14px;
        opacity: 0.8;
      }
    }
  }

  .weather {
    text-align: right;
    font-size: 14px;

    i {
      margin-right: 5px;
    }
  }
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  margin-bottom: 20px;

  .action-item {
    background: white;
    border-radius: 12px;
    padding: 20px 10px;
    text-align: center;
    cursor: pointer;
    transition: transform 0.2s;

    &:active {
      transform: scale(0.95);
    }

    .icon-wrapper {
      width: 40px;
      height: 40px;
      background: #f0f7ff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 10px;

      i {
        font-size: 20px;
        color: #1A7EFD;
      }
    }

    span {
      font-size: 12px;
      color: #333;
    }
  }
}

.today-overview,
.trend-chart,
.todo-section,
.notice-section {
  background: white;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;

  .section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    h3 {
      margin: 0;
      font-size: 16px;
      color: #333;
    }

    .update-time {
      font-size: 12px;
      color: #999;
    }
  }
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;

  .overview-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    position: relative;

    .value {
      font-size: 20px;
      font-weight: bold;
      color: #333;
      margin-bottom: 5px;
    }

    .label {
      font-size: 12px;
      color: #666;
      margin-bottom: 8px;
    }

    .change {
      font-size: 12px;
      position: absolute;
      top: 10px;
      right: 10px;

      &.positive {
        color: #67c23a;
      }

      &.negative {
        color: #f56c6c;
      }
    }
  }
}

.chart-container {
  .chart {
    width: 100%;
    height: 200px;
  }
}

.todo-list {
  .todo-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;

    &:last-child {
      border-bottom: none;
    }

    .todo-content {
      flex: 1;

      .todo-title {
        font-size: 14px;
        color: #333;
        margin-bottom: 4px;
      }

      .todo-desc {
        font-size: 12px;
        color: #666;
      }
    }

    .todo-meta {
      text-align: right;

      .todo-time {
        font-size: 12px;
        color: #999;
        margin-bottom: 4px;
      }

      .todo-priority {
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 10px;

        &.high {
          background: #fef0f0;
          color: #f56c6c;
        }

        &.medium {
          background: #fdf6ec;
          color: #e6a23c;
        }
      }
    }
  }

  .empty-todo {
    text-align: center;
    padding: 40px 0;
    color: #999;

    i {
      font-size: 48px;
      margin-bottom: 10px;
      display: block;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }
}

.notice-list {
  .notice-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;

    &:last-child {
      border-bottom: none;
    }

    .notice-title {
      font-size: 14px;
      color: #333;
    }

    .notice-time {
      font-size: 12px;
      color: #999;
    }
  }
}
</style>
