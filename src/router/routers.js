import Vue from 'vue'
import Router from 'vue-router'
import Layout from '../layout/index'
import MobileLayout from '../layout/mobile/index'

Vue.use(Router)

export const constantRouterMap = [
  { path: '/login',
    meta: { title: '登录', noCache: true },
    component: (resolve) => require(['@/views/login'], resolve),
    hidden: true
  },
  // 移动端登录
  { path: '/mobile/login',
    meta: { title: '移动端登录', noCache: true },
    component: (resolve) => require(['@/views/mobile/login'], resolve),
    hidden: true
  },
  {
    path: '/404',
    component: (resolve) => require(['@/views/features/404'], resolve),
    hidden: true
  },
  {
    path: '/401',
    component: (resolve) => require(['@/views/features/401'], resolve),
    hidden: true
  },
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path*',
        component: (resolve) => require(['@/views/features/redirect'], resolve)
      }
    ]
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: (resolve) => require(['@/views/home'], resolve),
        name: 'Dashboard',
        meta: { title: '首页', icon: 'index', affix: true, noCache: true }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'center',
        component: (resolve) => require(['@/views/system/user/center'], resolve),
        name: '个人中心',
        meta: { title: '个人中心' }
      }
    ]
  },
  {
    path: '/asset',
    component: Layout,
    children: [
      {
        path: 'details',
        hidden: true,
        component: (resolve) => require(['@/views/projectManage/asset/details/index'], resolve),
        name: 'AssetDtl',
        meta: { title: '资产方详情' }
      }
    ]
  },
  {
    path: '/guarantee',
    component: Layout,
    children: [
      {
        path: 'details',
        hidden: true,
        component: (resolve) => require(['@/views/projectManage/guarantee/details/index'], resolve),
        name: 'GuaranteeDtl',
        meta: { title: '融担方详情' }
      }
    ]
  },
  {
    path: '/fund',
    component: Layout,
    children: [
      {
        path: 'details',
        hidden: true,
        component: (resolve) => require(['@/views/projectManage/fund/details/index'], resolve),
        name: 'FundDtl',
        meta: { title: '资金方详情' }
      }
    ]
  },
  {
    path: '/project',
    component: Layout,
    children: [
      {
        path: 'details',
        hidden: true,
        component: (resolve) => require(['@/views/projectManage/project/details/index'], resolve),
        name: 'ProjectDtl',
        meta: { title: '项目详情' }
      }
    ]
  },
  // 移动端路由
  {
    path: '/mobile',
    component: MobileLayout,
    redirect: '/mobile/dashboard',
    hidden: true,
    children: [
      {
        path: 'dashboard',
        component: (resolve) => require(['@/views/mobile/dashboard/index'], resolve),
        name: 'MobileDashboard',
        meta: { title: '首页', showTabbar: true }
      },
      {
        path: 'report',
        component: (resolve) => require(['@/views/mobile/report/index'], resolve),
        name: 'MobileReport',
        meta: { title: '数据报表', showTabbar: true }
      },
      {
        path: 'profile',
        component: (resolve) => require(['@/views/mobile/profile/index'], resolve),
        name: 'MobileProfile',
        meta: { title: '个人中心', showTabbar: true }
      }
    ]
  }
]

export default new Router({
  // mode: 'hash',
  mode: 'history',
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRouterMap
})
