/*
*  Copyright 2019-2025 <PERSON>e
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
* @description /
* <AUTHOR>
* @date 2025-08-21
**/
@Data
public class ProjectContractDto implements Serializable {

    /**
     * 主键ID
     *
     */
    private String id;
    /**
     * 项目唯一编码
     */
    private String projectCode;

    /**
     * 合同模板唯一编码
     */
    private String contractCode;

    /**
     * 合同英文简称(文件名)
     */
    private String contractFileName;

    /**
     * 合同模板中文描述
     */
    private String contractDescription;

    /**
     * 合同归属方  FLOW-资产; BANK-资金 GUARANTEE-融担
     */
    private String contractOwner;

    /**
     * 甲方
     */
    private String partyA;

    /**
     * 乙方
     */
    private String partyB;

    /**
     *  合同开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime contractStartTime;

    /**
     *  合同结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime contractEndTime;

    /**
     *  禁用启用状态
     */
    private String enabled;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     *  更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件扩展名
     */
    private String extension;

    /**
     * 扩展字段1
     */
    private String ext1;

    /**
     *扩展字段2
     */
    private String ext2;

    @NotBlank
    private String contractType;


}
