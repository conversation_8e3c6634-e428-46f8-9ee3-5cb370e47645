<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jinghang.cash.modules.ops.mapper.OperationDayPlanMapper">
    <resultMap id="BaseResultMap" type="com.jinghang.cash.modules.ops.domain.OperationDayPlan">
        <id column="id" property="id"/>
        <result column="busi_code" property="busiCode"/>
        <result column="plan_date" property="planDate"/>
        <result column="plan_amount" property="planAmount"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="creater" property="creater"/>
        <result column="update_time" property="updateTime"/>
        <result column="updater" property="updater"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, busi_code, plan_date, plan_amount, deleted, create_time, creater, update_time, updater
    </sql>


    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO history_summary
        (busi_code,
        plan_date,
        plan_amount,
        deleted,
        create_time,
        creater,
        update_time,
        updater)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.busiCode},
            #{item.planDate},
            #{item.planAmount},
            #{item.deleted},
            #{item.createTime},
            #{item.creater},
            #{item.updateTime},
            #{item.updater})
        </foreach>
    </insert>


    <select id="findAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from operation_day_plan
        <where>
        </where>
        order by id desc
    </select>
    <select id="getOperationDayPlanVos"
            resultType="com.jinghang.cash.modules.ops.domain.vo.OperationDayPlanVo">
        select
        t3.plan_code as planCode,
        t3.capital_code as capitalCode,
        t3.capital_short as capitalShort,
        t3.plan_month as planMonth,
        t3.plan_amount as planAmount,
        t3.remark as remark,
        t2.project_code as projectCode,
        t2.project_name as projectName,
        t2.project_amount as projectAmount,
        t1.plan_amount as planDayAmount,
        t1.plan_date as planDate,
        t3.id as planId,
        t1.id as id
        from operation_day_plan as t1
        LEFT JOIN operation_project_plan as t2 on t1.busi_code = t2.busi_code
        LEFT JOIN operation_month_plan as t3 on t2.plan_code = t3.plan_code
        where
        t3.plan_code = #{planCode}
        t3.plan_month = #{planMonth}
    </select>
</mapper>