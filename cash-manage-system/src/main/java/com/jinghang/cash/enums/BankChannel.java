package com.jinghang.cash.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum BankChannel {

    TC_ZB("同程众邦"),

    QJ_LSB("亲家临商"),

    RL_CYCFC("润楼长银"),

    /**
     * 亲家亿联F003
     */
    QJ_YL_F003("亲家亿联"),

    ZY_EBANK("中原消金-轻花"),

    LH_RL_QH("蓝海润楼-轻花"),

    QJ_HRB("亲家华瑞"),

    RL_QDB("润楼青岛"),

    ZXB("振兴银行直连"),

    ZY_ZB_QH("中原中保轻花"),

    QJ_LZB("亲家兰州银行"),

    GMXT_TZ30_2_1("国民信托-天泽30-2-1"),

    CYBK("长银消金直连"),
    HXBK("湖消"),

    JMX_ZX("金美信振兴"),

    ZZ_LN("中置-辽农"),

    HAIER_QH("轻花-海尔-海峡"),

    QJ_LHB("亲家蓝海"),

    QJ_LH_ZS("亲家-蓝海-中世普惠"),

    CYBK_FL("长银分润"),

    ZZR_ZB("众智融-众邦"),

    ZXB_NUL("振兴银行直连非联合贷"),

    RL_CYCFC_FL("润楼长银分润"),

    TL_SMB("通联-苏商银行"),

    FBANK_LYX("富民银行"),

    ZXB_ZGC("振兴银行直连-中关村联合贷"),

    ZKJ_LN("中科金-辽农"),

    HQB_CYB("花期宝-长银"),

    HAIER_LH_QH("海尔蓝海");

    private final String name;

}
