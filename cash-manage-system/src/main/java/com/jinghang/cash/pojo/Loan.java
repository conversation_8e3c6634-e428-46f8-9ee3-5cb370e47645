package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * @TableName loan
 */
@TableName(value ="loan")
@Data
public class Loan implements Serializable {
    private String id;

    private String userId;

    private String orderId;

    private String creditId;

    /**
     * 放款记录id
     */
    private String loanRecordId;

    private String flowChannel;

    private String outerLoanId;

    private Date applyTime;

    private BigDecimal amount;

    private Integer periods;

    private String loanPurpose;

    private String loanCardId;

    private String repayCardId;

    private String packageId;

    private String loanState;

    private String bankChannel;

    private String loanNo;

    private Date loanTime;

    private String planSyncCore;

    private String failReason;

    private String loanContractNo;

    private String remark;

    private String revision;

    private String createdBy;

    private Date createdTime;

    private String updatedBy;

    private Date updatedTime;

    private BigDecimal bankRate;

    private BigDecimal irrRate;

    private String rightsDeductState;

    private static final long serialVersionUID = 1L;
}
