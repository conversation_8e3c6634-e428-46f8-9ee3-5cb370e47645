/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.ops.domain.vo;

import com.jinghang.cash.api.enums.AbleStatus;
import com.jinghang.cash.modules.ops.domain.OperationMonthPlan;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;

/**
* @description /
* <AUTHOR>
* @date 2025-09-15
**/
@Data
public class OperationMonthPlanVo extends OperationMonthPlan implements Serializable {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "放款计划配置编码")
    private String planCode;

    @ApiModelProperty(value = "所属月份 格式 yyyyMM")
    private Integer planMonth;

    @ApiModelProperty(value = "资金方编码")
    private String capitalCode;

    @ApiModelProperty(value = "资金方简称")
    private String capitalShort;

    @ApiModelProperty(value = "计划总金额")
    private BigDecimal planAmount;

    @ApiModelProperty(value = "备注")
    private String remark;

    @NotBlank
    @ApiModelProperty(value = "删除标识(Y-是，N 否)")
    private String deleted;

    @ApiModelProperty(value = "数据状态 (默认未启用 未启用：可删除，可逐一更改行信息 启用：不可删除，可修改行信息)")
    private AbleStatus enable;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    private String creater;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "修改人")
    private String updater;

}
