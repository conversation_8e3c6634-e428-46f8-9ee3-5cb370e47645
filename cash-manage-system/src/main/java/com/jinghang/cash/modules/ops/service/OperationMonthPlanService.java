/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.ops.service;

import com.jinghang.cash.modules.ops.domain.OperationMonthPlan;
import com.jinghang.cash.modules.ops.domain.dto.OperationMonthPlanDto;
import java.util.List;
import java.io.IOException;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghang.cash.modules.ops.domain.vo.OperationMonthPlanVo;
import com.jinghang.cash.modules.project.domain.ProjectContract;
import com.jinghang.cash.utils.PageResult;

/**
* @description 服务接口
* <AUTHOR>
* @date 2025-09-15
**/
public interface OperationMonthPlanService extends IService<OperationMonthPlan> {

    /**
    * 查询数据分页
    * @param dto 条件
    * @return PageResult
    */
    PageResult<OperationMonthPlanVo> queryAllPage(OperationMonthPlanDto dto);

    /**
    * 查询所有数据不分页
    * @param dto 条件参数
    * @return List<OperationMonthPlan>
    */
    List<OperationMonthPlan> queryAll(OperationMonthPlanDto dto);

    /**
    * 创建
    * @param resources /
    */
    Map<String, Object> create(OperationMonthPlanDto resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(OperationMonthPlan resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(List<Long> ids);

    /**
     * 查询月放款计划详情
     * @param id 条件参数
     * @return OperationMonthPlanVo
     */
    OperationMonthPlanVo info(String id);

    /**
     * 启用/停用月放款计划
     * @param resources /
     */
    void stopOperationMonthPlan(OperationMonthPlanDto resources);

    /**
     * 查询所有已启用资金方列表
     * @param
     */
    List<Map<String, String>> queryCapital();

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<OperationMonthPlan> all, HttpServletResponse response) throws IOException;
}