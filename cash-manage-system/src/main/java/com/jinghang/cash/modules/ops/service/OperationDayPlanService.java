/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.ops.service;

import com.jinghang.cash.modules.ops.domain.OperationDayPlan;
import com.jinghang.cash.modules.ops.domain.dto.OperationDayPlanDto;

import java.time.LocalDate;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghang.cash.modules.ops.domain.vo.OperationPlanVo;
import com.jinghang.cash.utils.PageResult;
import org.apache.ibatis.annotations.Param;

/**
* @description 服务接口
* <AUTHOR>
* @date 2025-09-15
**/
public interface OperationDayPlanService extends IService<OperationDayPlan> {

    /**
    * 查询数据分页
    * @param dto 条件
    * @return PageResult
    */
    PageResult<OperationDayPlan> queryAllPage(OperationDayPlanDto dto);

    /**
    * 查询所有数据不分页
    * @param dto 条件参数
    * @return List<OperationDayPlan>
    */
    List<OperationDayPlan> queryAll(OperationDayPlanDto dto);

    /**
    * 创建
    * @param resources /
    */
    void create(OperationDayPlan resources);

    /**
     * 批量编辑 日计划配额
     * @param dtos
     */
    void batchEdit(List<OperationDayPlanDto> dtos);

    /**
     * 创建日计划配置
     * @param list
     */
    void createPlan(List<OperationDayPlanDto> list);

    /**
    * 编辑
    * @param resources /
    */
    void update(OperationDayPlan resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(List<Integer> ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<OperationDayPlan> all, HttpServletResponse response) throws IOException;

    /**
     * 根据 月度计划 编码查询 日配置详情
     * @param planCode
     * @param month
     * @return
     */
    OperationPlanVo getOperationPlanVo(String planCode,Integer month);
}