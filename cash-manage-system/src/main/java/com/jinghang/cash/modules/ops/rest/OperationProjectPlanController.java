/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.ops.rest;

import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.ops.domain.OperationProjectPlan;
import com.jinghang.cash.modules.ops.service.OperationProjectPlanService;
import com.jinghang.cash.modules.ops.domain.dto.OperationProjectPlanDto;
import com.jinghang.cash.utils.PageResult;
import lombok.RequiredArgsConstructor;
import java.util.List;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2025-09-15
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "ProjectPlan")
@RequestMapping("/api/operationProjectPlan")
public class OperationProjectPlanController {

    private final OperationProjectPlanService operationProjectPlanService;

    @GetMapping("/queryPage")
    @ApiOperation("查询ProjectPlan")
    @PreAuthorize("@el.check('operationProjectPlan:list')")
    public RestResult<PageResult<OperationProjectPlan>> queryOperationProjectPlan(OperationProjectPlanDto dto){
        return RestResult.success(operationProjectPlanService.queryAllPage(dto));
    }

    @PostMapping("/create")
    @ApiOperation("新增项目计划")
    @PreAuthorize("@el.check('operationProjectPlan:add')")
    public RestResult<Object> createOperationProjectPlan(@Validated @RequestBody List<OperationProjectPlanDto> resources){
        operationProjectPlanService.create(resources);
        return RestResult.success(HttpStatus.CREATED);
    }

    @PutMapping("/update")
    @ApiOperation("修改ProjectPlan")
    @PreAuthorize("@el.check('operationProjectPlan:edit')")
    public RestResult<Object> updateOperationProjectPlan(@Validated @RequestBody OperationProjectPlan resources){
        operationProjectPlanService.update(resources);
        return RestResult.success(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping("/del")
    @ApiOperation("删除ProjectPlan")
    @PreAuthorize("@el.check('operationProjectPlan:del')")
    public RestResult<Object> deleteOperationProjectPlan(@ApiParam(value = "传ID数组[]") @RequestBody List<Integer> ids) {
        operationProjectPlanService.deleteAll(ids);
        return RestResult.success(HttpStatus.OK);
    }

    @GetMapping("/queryProject")
    @ApiOperation("查询当前资金方关联的所有已启用项目")
    @PreAuthorize("@el.check('operationProjectPlan:add')")
    public RestResult<List<Map<String, String>>> queryProject(@RequestParam(name = "capitalCode") String capitalCode){
        return RestResult.success(operationProjectPlanService.queryProject(capitalCode));
    }

    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('operationProjectPlan:list')")
    public void exportOperationProjectPlan(HttpServletResponse response, OperationProjectPlanDto dto) throws IOException {
    operationProjectPlanService.download(operationProjectPlanService.queryAll(dto), response);
    }
}