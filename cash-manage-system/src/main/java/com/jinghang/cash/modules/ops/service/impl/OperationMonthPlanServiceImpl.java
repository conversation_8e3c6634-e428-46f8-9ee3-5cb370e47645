/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.ops.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jinghang.cash.api.enums.AbleStatus;
import com.jinghang.cash.api.enums.AbleStatusExt;
import com.jinghang.cash.api.enums.ActiveInactive;
import com.jinghang.cash.modules.ops.domain.OperationMonthPlan;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghang.cash.modules.ops.domain.vo.OperationMonthPlanVo;
import com.jinghang.cash.modules.ops.service.OperationProjectPlanService;
import com.jinghang.cash.modules.project.domain.CapitalConfig;
import com.jinghang.cash.modules.project.mapper.CapitalConfigMapper;
import com.jinghang.cash.utils.SecurityUtils;
import com.jinghang.cash.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.cash.modules.ops.service.OperationMonthPlanService;
import com.jinghang.cash.modules.ops.domain.dto.OperationMonthPlanDto;
import com.jinghang.cash.modules.ops.mapper.OperationMonthPlanMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.jinghang.cash.utils.PageUtil;

import java.time.LocalDateTime;
import java.util.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;

import com.jinghang.cash.utils.PageResult;

/**
* @description 服务实现
* <AUTHOR>
* @date 2025-09-15
**/
@Service
@RequiredArgsConstructor
public class OperationMonthPlanServiceImpl extends ServiceImpl<OperationMonthPlanMapper, OperationMonthPlan> implements OperationMonthPlanService {

    private final OperationMonthPlanMapper operationMonthPlanMapper;

    private final CapitalConfigMapper capitalConfigMapper;

    private final OperationProjectPlanService operationProjectPlanService;

    @Override
    public PageResult<OperationMonthPlanVo> queryAllPage(OperationMonthPlanDto criteria){
        // 获取分页参数
        Page<OperationMonthPlan> page = new Page<>(criteria.getPage(), criteria.getSize());
        // 设置查询条件
        LambdaQueryWrapper<OperationMonthPlan> wrapper = new LambdaQueryWrapper<OperationMonthPlan>();
        if (ObjectUtil.isNotEmpty(criteria.getPlanCode())){
            wrapper.eq(OperationMonthPlan::getPlanCode, criteria.getPlanCode());
        }
        if (ObjectUtil.isNotEmpty(criteria.getStartMonth())){
            wrapper.ge(OperationMonthPlan::getPlanMonth, criteria.getStartMonth());
        }
        if (ObjectUtil.isNotEmpty(criteria.getEndMonth())){
            wrapper.le(OperationMonthPlan::getPlanMonth, criteria.getEndMonth());
        }
        if (ObjectUtil.isNotEmpty(criteria.getEnable())){
            wrapper.eq(OperationMonthPlan::getEnable, criteria.getEnable());
        }
        if (ObjectUtil.isNotEmpty(criteria.getCapitalCode())){
            wrapper.eq(OperationMonthPlan::getCapitalCode, criteria.getCapitalCode());
        }
        wrapper.eq(OperationMonthPlan::getDeleted, ActiveInactive.N.getCode());
        // 查询数据库
        page = operationMonthPlanMapper.selectPage(page, wrapper);
        // 结果集转换为 vo对象
        List<OperationMonthPlanVo> result = page.getRecords().stream().map(item -> {
            OperationMonthPlanVo vo = new OperationMonthPlanVo();
            BeanUtils.copyProperties(item, vo);
            return vo;
        }).collect(Collectors.toList());
        // 返回查询结果
        return PageUtil.toPage(result, page.getTotal());
    }

    @Override
    public List<OperationMonthPlan> queryAll(OperationMonthPlanDto criteria){
        return operationMonthPlanMapper.findAll(criteria);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> create(OperationMonthPlanDto resources) {
        // 查询当前月度计划业务主键最大值
        int max;
        LambdaQueryWrapper<OperationMonthPlan> wrapper = new LambdaQueryWrapper<OperationMonthPlan>();
        wrapper.eq(OperationMonthPlan::getPlanMonth, resources.getPlanMonth());
        List<OperationMonthPlan> operationMonthPlans = operationMonthPlanMapper.selectList(wrapper);
        if (operationMonthPlans.size() != 0) {
            List<Integer> collect = operationMonthPlans.stream()
                    .map(item -> Integer.parseInt(item.getPlanCode().substring(item.getPlanCode().length() - 3)))
                    .collect(Collectors.toList());
            max = collect.stream().max(Integer::compare).get() + 1;
        } else {
            max = 1;
        }
        // 转换新增参数为 po对象
        OperationMonthPlan operationMonthPlan = new OperationMonthPlan();
        BeanUtils.copyProperties(resources, operationMonthPlan);
        // 设置创建人，创建时间，更新人，更新时间
        String format = String.format("%03d", max);
        operationMonthPlan.setPlanCode("LP" + operationMonthPlan.getPlanMonth() + format);
        operationMonthPlan.setEnable(AbleStatus.DISABLE);
        operationMonthPlan.setDeleted(ActiveInactive.N.getCode());
        operationMonthPlan.setCreater(String.valueOf(SecurityUtils.getCurrentUserId()));
        operationMonthPlan.setCreateTime(LocalDateTime.now());
        operationMonthPlan.setUpdater(String.valueOf(SecurityUtils.getCurrentUserId()));
        operationMonthPlan.setUpdateTime(LocalDateTime.now());
        // 插入数据库
        operationMonthPlanMapper.insert(operationMonthPlan);
        List<Map<String, String>> maps = operationProjectPlanService.queryProject(operationMonthPlan.getCapitalCode());
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(operationMonthPlan));
        map.put("project", maps);
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(OperationMonthPlan resources) {
        OperationMonthPlan operationMonthPlan = getById(resources.getId());
        operationMonthPlan.copy(resources);
        operationMonthPlanMapper.updateById(operationMonthPlan);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(List<Long> ids) {
        ids.forEach(item -> {
            OperationMonthPlan operationMonthPlan = new OperationMonthPlan();
            operationMonthPlan.setId(item);
            operationMonthPlan.setDeleted(ActiveInactive.Y.getCode());
            operationMonthPlanMapper.updateById(operationMonthPlan);
        });
    }

    @Override
    public OperationMonthPlanVo info(String id) {
        // 查询数据库
        OperationMonthPlan operationMonthPlan = operationMonthPlanMapper.selectById(id);
        // 转换结果为 vo对象
        OperationMonthPlanVo operationMonthPlanVo = new OperationMonthPlanVo();
        BeanUtils.copyProperties(operationMonthPlan, operationMonthPlanVo);
        return operationMonthPlanVo;
    }

    @Override
    public void stopOperationMonthPlan(OperationMonthPlanDto resources) {
        // 转换新增参数为 po对象
        OperationMonthPlan operationMonthPlan = new OperationMonthPlan();
        BeanUtils.copyProperties(resources, operationMonthPlan);
        operationMonthPlan.setUpdater(String.valueOf(SecurityUtils.getCurrentUserId()));
        operationMonthPlan.setUpdateTime(LocalDateTime.now());
        // 修改数据库
        operationMonthPlanMapper.updateById(operationMonthPlan);
    }

    @Override
    public List<Map<String, String>> queryCapital() {
        LambdaQueryWrapper<CapitalConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CapitalConfig::getEnabled, AbleStatusExt.ENABLE.name());
        List<CapitalConfig> capitalConfigs = capitalConfigMapper.selectList(wrapper);
        List<Map<String, String>> result = capitalConfigs.stream().map(item -> {
            Map<String, String> vo = new HashMap<>();
            vo.put("capitalCode", item.getBankChannel());
            vo.put("capitalShort", item.getCapitalNameShort());
            return vo;
        }).collect(Collectors.toList());
        return result;
    }

    @Override
    public void download(List<OperationMonthPlan> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (OperationMonthPlan operationMonthPlan : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("放款计划配置编码", operationMonthPlan.getPlanCode());
            map.put("所属月份 格式 yyyyMM", operationMonthPlan.getPlanMonth());
            map.put("资金方编码", operationMonthPlan.getCapitalCode());
            map.put("资金方简称", operationMonthPlan.getCapitalShort());
            map.put("计划总金额", operationMonthPlan.getPlanAmount());
            map.put("备注", operationMonthPlan.getRemark());
            map.put("删除标识(Y-是，N 否)", operationMonthPlan.getDeleted());
            map.put("数据状态 (默认未启用 未启用：可删除，可逐一更改行信息 启用：不可删除，可修改行信息)", operationMonthPlan.getEnable());
            map.put("创建时间", operationMonthPlan.getCreateTime());
            map.put("创建人", operationMonthPlan.getCreater());
            map.put("修改时间", operationMonthPlan.getUpdateTime());
            map.put("修改人", operationMonthPlan.getUpdater());
            list.add(map);
        }
        com.jinghang.cash.utils.FileUtil.downloadExcel(list, response);
    }
}