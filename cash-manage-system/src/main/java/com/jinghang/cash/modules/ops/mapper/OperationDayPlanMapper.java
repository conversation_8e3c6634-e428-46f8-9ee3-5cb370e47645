/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.ops.mapper;

import com.jinghang.cash.modules.ops.domain.OperationDayPlan;
import com.jinghang.cash.modules.ops.domain.dto.OperationDayPlanDto;

import java.time.LocalDate;
import java.util.List;

import com.jinghang.cash.modules.ops.domain.vo.OperationDayPlanVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
* <AUTHOR>
* @date 2025-09-15
**/
@Mapper
public interface OperationDayPlanMapper extends BaseMapper<OperationDayPlan> {

    IPage<OperationDayPlan> findAll(@Param("dto") OperationDayPlanDto dto, Page<Object> page);

    List<OperationDayPlan> findAll(@Param("dto") OperationDayPlanDto dto);

    int insertBatch(@Param("list") List<OperationDayPlan> list);

    int batchUpdate(@Param("list") List<OperationDayPlanDto> list);

    List<OperationDayPlanVo> getOperationDayPlanVos(@Param("planCode")String planCode, @Param("month")Integer month);


}