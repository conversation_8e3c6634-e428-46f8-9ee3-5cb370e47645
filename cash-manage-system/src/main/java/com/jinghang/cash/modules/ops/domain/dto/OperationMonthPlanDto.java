/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.ops.domain.dto;

import com.jinghang.cash.api.enums.AbleStatus;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
* <AUTHOR>
* @date 2025-09-15
**/
@Data
public class OperationMonthPlanDto{

    @ApiModelProperty(value = "页码", example = "1")
    private Integer page = 1;

    @ApiModelProperty(value = "每页数据量", example = "10")
    private Integer size = 10;

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "放款计划配置编码")
    private String planCode;

    @ApiModelProperty(value = "开始月份", example = "yyyyMM")
    private Integer startMonth;

    @ApiModelProperty(value = "结束月份", example = "yyyyMM")
    private Integer endMonth;

    @ApiModelProperty(value = "数据状态 (默认未启用 未启用：可删除，可逐一更改行信息 启用：不可删除，可修改行信息)")
    private AbleStatus enable;

    @ApiModelProperty(value = "所属月份 格式 yyyyMM")
    private Integer planMonth;

    @ApiModelProperty(value = "资金方编码")
    private String capitalCode;

    @ApiModelProperty(value = "资金方简称")
    private String capitalShort;

    @ApiModelProperty(value = "计划总金额")
    private BigDecimal planAmount;

    @ApiModelProperty(value = "备注")
    private String remark;
}