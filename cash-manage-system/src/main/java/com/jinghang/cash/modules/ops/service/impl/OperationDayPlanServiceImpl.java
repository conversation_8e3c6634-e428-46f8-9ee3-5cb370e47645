/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.ops.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.jinghang.cash.api.enums.ActiveInactive;
import com.jinghang.cash.modules.ops.domain.OperationDayPlan;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghang.cash.modules.ops.domain.OperationMonthPlan;
import com.jinghang.cash.modules.ops.domain.OperationProjectPlan;
import com.jinghang.cash.modules.ops.domain.dto.OperationDayPlanDto;
import com.jinghang.cash.modules.ops.domain.vo.OperationDayPlanVo;
import com.jinghang.cash.modules.ops.domain.vo.OperationPlanVo;
import com.jinghang.cash.modules.ops.mapper.OperationMonthPlanMapper;
import com.jinghang.cash.modules.ops.mapper.OperationProjectPlanMapper;
import com.jinghang.cash.utils.*;
import com.jinghang.common.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.cash.modules.ops.service.OperationDayPlanService;
import com.jinghang.cash.modules.ops.mapper.OperationDayPlanMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
* @description 服务实现
* <AUTHOR>
* @date 2025-09-15
**/
@Service
@RequiredArgsConstructor
public class OperationDayPlanServiceImpl extends ServiceImpl<OperationDayPlanMapper, OperationDayPlan> implements OperationDayPlanService {

    private final OperationDayPlanMapper operationDayPlanMapper;

    private final OperationMonthPlanMapper operationMonthPlanMapper;

    private final OperationProjectPlanMapper operationProjectPlanMapper;


    @Override
    public PageResult<OperationDayPlan> queryAllPage(OperationDayPlanDto criteria){
        Page<OperationDayPlan> page = new Page<>(criteria.getPage(), criteria.getSize());
        LambdaQueryWrapper<OperationDayPlan> wrapper = new LambdaQueryWrapper<OperationDayPlan>();
        // TODO: 根据实际需要添加查询条件
        page = operationDayPlanMapper.selectPage(page, wrapper);
        return PageUtil.toPage(page.getRecords(), page.getTotal());
    }

    @Override
    public List<OperationDayPlan> queryAll(OperationDayPlanDto dto) {
        return List.of();
    }


    @Override
    public void createPlan(List<OperationDayPlanDto> list) {
        if(CollectionUtil.isEmpty(list)){
            throw new RuntimeException("请选择配置项目");
        }
        boolean months = list.stream().anyMatch(x -> ObjectUtil.isEmpty(x.getPlanMonth()));
        if(months){
            throw new RuntimeException("项目计划月份不能为空");
        }
        boolean amounts = list.stream().anyMatch(x -> ObjectUtil.isEmpty(x.getPlanAmount()));
        if(amounts){
            throw new RuntimeException("计划金额不能为空");
        }
        List<OperationDayPlan> result = new CopyOnWriteArrayList<>();
        String code = ActiveInactive.N.getCode();
        LocalDateTime time = LocalDateTime.now();
        String userId = String.valueOf(SecurityUtils.getCurrentUserId());
        list.parallelStream().forEach(x->{
            String[] split = x.getPlanMonth().split("-");
            Map<Integer, Double> map = DailyDataAllocator.getDailyDataAllocator(String.valueOf(x.getPlanAmount()), x.getPlanMonth(), 0);
            map.forEach((k,v)->{
                OperationDayPlan plan = new OperationDayPlan();
                plan.setPlanDate(LocalDate.of(Integer.parseInt(split[0]),Integer.parseInt(split[1]),k));
                plan.setPlanAmount(new BigDecimal(v));
                plan.setDeleted(code);
                plan.setBusiCode(x.getBusiCode());
                plan.setCreateTime(time);
                plan.setCreater(userId);
                plan.setUpdateTime(time);
                plan.setUpdater(userId);
                result.add(plan);
            });

        });
        operationDayPlanMapper.insertBatch(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(OperationDayPlan resources) {
        operationDayPlanMapper.insert(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchEdit(List<OperationDayPlanDto> dtos) {
        if(CollectionUtil.isEmpty(dtos)){
            throw new RuntimeException("请选择数据");
        }
        boolean ids = dtos.stream().anyMatch(x -> ObjectUtil.isEmpty(x.getId()));
        if(ids){
            throw new RuntimeException("日计划id不能为空");
        }
        boolean codes = dtos.stream().anyMatch(x -> ObjectUtil.isEmpty(x.getBusiCode()));
        if(codes){
            throw new RuntimeException("业务编码不能为空");
        }
        boolean amounts = dtos.stream().anyMatch(x -> ObjectUtil.isEmpty(x.getPlanAmount()));
        if(amounts){
            throw new RuntimeException("日计划配额不能为空");
        }
        // 1 修改日计划金额
        Map<String, List<OperationDayPlanDto>> collect = this.updateDayPlan(dtos);
        AtomicReference<String> planCode = new AtomicReference<>();
        collect.forEach((k,v)->{
            BigDecimal reduce = v.stream().map(OperationDayPlanDto::getPlanAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 2 修改项目计划金额
            planCode.set(updateProject(k, reduce));
        });
        BigDecimal reduce = dtos.stream().map(OperationDayPlanDto::getPlanAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 3 修改月度计划金额
        this.updateMonthPlan(planCode.get(),reduce);
    }

    /**
     *  修改日配额
     */
    private Map<String, List<OperationDayPlanDto>> updateDayPlan(List<OperationDayPlanDto> dtos){
        LocalDateTime time = LocalDateTime.now();
        String userId = String.valueOf(SecurityUtils.getCurrentUserId());
        dtos.forEach(x->{
            x.setUpdater(userId);
            x.setUpdateTime(time);
        });
        operationDayPlanMapper.batchUpdate(dtos);
        return dtos.stream().collect(Collectors.groupingBy(OperationDayPlanDto::getBusiCode));
    }

    /**
     *  修改项目金额
     * @param code
     * @param amount
     */
    private String updateProject(String code,BigDecimal amount){
        LambdaQueryWrapper<OperationProjectPlan> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OperationProjectPlan::getBusiCode,code);
        lambdaQueryWrapper.eq(OperationProjectPlan::getDeleted,"N");
        OperationProjectPlan projectPlan = operationProjectPlanMapper.selectOne(lambdaQueryWrapper);
        projectPlan.setProjectAmount(amount);
        projectPlan.setUpdater(String.valueOf(SecurityUtils.getCurrentUserId()));
        projectPlan.setUpdateTime(LocalDateTime.now());
        operationProjectPlanMapper.updateById(projectPlan);
        return projectPlan.getPlanCode();
    }

    /**
     *  修改月度计划金额
     * @param code
     * @param amount
     */
    private void updateMonthPlan(String code,BigDecimal amount){
        LambdaQueryWrapper<OperationMonthPlan> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OperationMonthPlan::getPlanCode,code);
        lambdaQueryWrapper.eq(OperationMonthPlan::getDeleted,"N");
        OperationMonthPlan plan = operationMonthPlanMapper.selectOne(lambdaQueryWrapper);
        plan.setPlanAmount(amount);
        plan.setUpdater(String.valueOf(SecurityUtils.getCurrentUserId()));
        plan.setUpdateTime(LocalDateTime.now());
        operationMonthPlanMapper.updateById(plan);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(OperationDayPlan resources) {
        OperationDayPlan operationDayPlan = getById(resources.getId());
        operationDayPlan.copy(resources);
        operationDayPlanMapper.updateById(operationDayPlan);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(List<Integer> ids) {
        operationDayPlanMapper.deleteBatchIds(ids);
    }

    @Override
    public void download(List<OperationDayPlan> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (OperationDayPlan operationDayPlan : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("关联project_plan的busi_code", operationDayPlan.getBusiCode());
            map.put("计划日期", operationDayPlan.getPlanDate());
            map.put("日计划金额", operationDayPlan.getPlanAmount());
            map.put("删除标识(Y-是，N 否)", operationDayPlan.getDeleted());
            map.put("创建时间", operationDayPlan.getCreateTime());
            map.put("创建人", operationDayPlan.getCreater());
            map.put("修改时间", operationDayPlan.getUpdateTime());
            map.put("修改人", operationDayPlan.getUpdater());
            list.add(map);
        }
        com.jinghang.cash.utils.FileUtil.downloadExcel(list, response);
    }

    @Override
    public OperationPlanVo getOperationPlanVo(String planCode, Integer month) {
        OperationPlanVo vo  = new OperationPlanVo();
        List<OperationDayPlanVo> vos = operationDayPlanMapper.getOperationDayPlanVos(planCode, month);
        Map<String, List<OperationDayPlanVo>> collect = vos.stream().collect(Collectors.groupingBy(OperationDayPlanVo::getPlanCode));
        collect.forEach((k,v)->{
            OperationDayPlanVo planVo = v.get(0);
            BeanUtil.copyProperties(planVo,vo);
            vo.setVos(v);
        });
        return vo;
    }
}