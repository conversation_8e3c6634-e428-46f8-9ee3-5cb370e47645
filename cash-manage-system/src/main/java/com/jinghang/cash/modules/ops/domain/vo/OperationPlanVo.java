/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.ops.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
* @description /
* <AUTHOR>
* @date 2025-09-15
**/
@Data
public class OperationPlanVo implements Serializable {

    @ApiModelProperty(value = "计划主键id")
    private Integer planId;

    @ApiModelProperty(value = "放款配置编码")
    private String planCode;

    @ApiModelProperty(value = "资金方编码")
    private String capitalCode;

    @ApiModelProperty(value = "资金方名称")
    private String capitalShort;

    @ApiModelProperty(value = "所属月份")
    private String planMonth;

    @ApiModelProperty(value = "计划总金额")
    private BigDecimal planAmount;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "日计划明细")
    private List<OperationDayPlanVo> vos;



}
