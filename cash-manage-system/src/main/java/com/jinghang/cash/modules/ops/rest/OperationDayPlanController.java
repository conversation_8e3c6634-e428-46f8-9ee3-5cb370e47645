/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.ops.rest;

import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.ops.domain.OperationDayPlan;
import com.jinghang.cash.modules.ops.domain.vo.OperationPlanVo;
import com.jinghang.cash.modules.ops.service.OperationDayPlanService;
import com.jinghang.cash.modules.ops.domain.dto.OperationDayPlanDto;
import com.jinghang.cash.utils.PageResult;
import lombok.RequiredArgsConstructor;
import java.util.List;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2025-09-15
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "DayPlan")
@RequestMapping("/api/operationDayPlan")
public class OperationDayPlanController {

    private final OperationDayPlanService operationDayPlanService;

    @GetMapping("/queryPage")
    @ApiOperation("查询DayPlan")
    @PreAuthorize("@el.check('operationDayPlan:page')")
    public RestResult<PageResult<OperationDayPlan>> queryOperationDayPlan(OperationDayPlanDto dto){
        return RestResult.success(operationDayPlanService.queryAllPage(dto));
    }



    @GetMapping("/queryDayPlanDetails")
    @ApiOperation("查询DayPlan详情")
    @PreAuthorize("@el.check('queryDayPlanDetails:dayPlanDetails')")
    public RestResult<OperationPlanVo> getOperationPlanVo(@RequestParam String planCode, @RequestParam Integer month){
        return RestResult.success(operationDayPlanService.getOperationPlanVo(planCode,month));
    }


    @PostMapping("/batchEdit")
    @ApiOperation("批量修改日配额")
    @PreAuthorize("@el.check('operationDayPlan:batchEdit')")
    public RestResult<Object> batchEdit(@Validated @RequestBody List<OperationDayPlanDto> dtos){
        operationDayPlanService.batchEdit(dtos);
        return RestResult.success();
    }

    @PostMapping("/createPlan")
    @ApiOperation("新增createPlan")
    @PreAuthorize("@el.check('operationDayPlan:createPlan')")
    public RestResult<Object> createPlan(@Validated @RequestBody List<OperationDayPlanDto> dtos){
        operationDayPlanService.createPlan(dtos);
        return RestResult.success(HttpStatus.CREATED);
    }


    @PostMapping("/create")
    @ApiOperation("新增DayPlan")
    @PreAuthorize("@el.check('operationDayPlan:add')")
    public RestResult<Object> createOperationDayPlan(@Validated @RequestBody OperationDayPlan resources){
        operationDayPlanService.create(resources);
        return RestResult.success(HttpStatus.CREATED);
    }

    @PutMapping("/update")
    @ApiOperation("修改DayPlan")
    @PreAuthorize("@el.check('operationDayPlan:edit')")
    public RestResult<Object> updateOperationDayPlan(@Validated @RequestBody OperationDayPlan resources){
        operationDayPlanService.update(resources);
        return RestResult.success(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping("/del")
    @ApiOperation("删除DayPlan")
    @PreAuthorize("@el.check('operationDayPlan:del')")
    public RestResult<Object> deleteOperationDayPlan(@ApiParam(value = "传ID数组[]") @RequestBody List<Integer> ids) {
        operationDayPlanService.deleteAll(ids);
        return RestResult.success(HttpStatus.OK);
    }

    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('operationDayPlan:list')")
    public void exportOperationDayPlan(HttpServletResponse response, OperationDayPlanDto dto) throws IOException {
    operationDayPlanService.download(operationDayPlanService.queryAll(dto), response);
    }
}