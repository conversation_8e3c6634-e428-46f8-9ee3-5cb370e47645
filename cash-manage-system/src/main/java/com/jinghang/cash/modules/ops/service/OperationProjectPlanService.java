/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.ops.service;

import com.jinghang.cash.modules.ops.domain.OperationProjectPlan;
import com.jinghang.cash.modules.ops.domain.dto.OperationProjectPlanDto;
import java.util.List;
import java.io.IOException;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghang.cash.utils.PageResult;

/**
* @description 服务接口
* <AUTHOR>
* @date 2025-09-15
**/
public interface OperationProjectPlanService extends IService<OperationProjectPlan> {

    /**
    * 查询数据分页
    * @param dto 条件
    * @return PageResult
    */
    PageResult<OperationProjectPlan> queryAllPage(OperationProjectPlanDto dto);

    /**
    * 查询所有数据不分页
    * @param dto 条件参数
    * @return List<OperationProjectPlan>
    */
    List<OperationProjectPlan> queryAll(OperationProjectPlanDto dto);

    /**
    * 创建
    * @param resources /
    */
    void create(List<OperationProjectPlanDto> resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(OperationProjectPlan resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(List<Integer> ids);

    /**
     * 查询当前资金方关联的所有已启用项目
     * @param
     */
    List<Map<String, String>> queryProject(String capitalCode);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<OperationProjectPlan> all, HttpServletResponse response) throws IOException;
}