/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.ops.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.sql.Timestamp;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* @description /
* <AUTHOR>
* @date 2025-09-15
**/
@Data
@TableName("operation_project_plan")
public class OperationProjectPlan implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "业务主键")
    private String busiCode;

    @ApiModelProperty(value = "放款计划配置编码")
    private String planCode;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目计划金额")
    private BigDecimal projectAmount;

    @NotBlank
    @ApiModelProperty(value = "删除标识(Y-是，N 否)")
    private String deleted;

    @NotNull
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @NotBlank
    @ApiModelProperty(value = "创建人")
    private String creater;

    @NotNull
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @NotBlank
    @ApiModelProperty(value = "修改人")
    private String updater;

    public void copy(OperationProjectPlan source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
