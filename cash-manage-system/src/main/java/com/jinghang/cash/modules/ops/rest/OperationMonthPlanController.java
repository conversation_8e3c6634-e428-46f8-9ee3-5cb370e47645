/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.ops.rest;

import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.ops.domain.OperationMonthPlan;
import com.jinghang.cash.modules.ops.domain.vo.OperationMonthPlanVo;
import com.jinghang.cash.modules.ops.service.OperationMonthPlanService;
import com.jinghang.cash.modules.ops.domain.dto.OperationMonthPlanDto;
import com.jinghang.cash.modules.project.domain.ProjectContract;
import com.jinghang.cash.utils.PageResult;
import lombok.RequiredArgsConstructor;
import java.util.List;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2025-09-15
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "MonthPlan")
@RequestMapping("/api/operationMonthPlan")
public class OperationMonthPlanController {

    private final OperationMonthPlanService operationMonthPlanService;

    @GetMapping("/queryPage")
    @ApiOperation("查询月放款计划列表-分页")
    @PreAuthorize("@el.check('operationMonthPlan:page')")
    public RestResult<PageResult<OperationMonthPlanVo>> queryOperationMonthPlan(OperationMonthPlanDto dto){
        return RestResult.success(operationMonthPlanService.queryAllPage(dto));
    }

    @PostMapping("/create")
    @ApiOperation("新增月放款计划")
    @PreAuthorize("@el.check('operationMonthPlan:add')")
    public RestResult<Object> createOperationMonthPlan(@Validated @RequestBody OperationMonthPlanDto resources){
        Map<String, Object> result = operationMonthPlanService.create(resources);
        return RestResult.success(result);
    }

    @PutMapping("/update")
    @ApiOperation("修改MonthPlan")
    @PreAuthorize("@el.check('operationMonthPlan:edit')")
    public RestResult<Object> updateOperationMonthPlan(@Validated @RequestBody OperationMonthPlan resources){
        operationMonthPlanService.update(resources);
        return RestResult.success(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/del")
    @ApiOperation("删除月放款计划")
    @PreAuthorize("@el.check('operationMonthPlan:del')")
    public RestResult<Object> deleteOperationMonthPlan(@ApiParam(value = "传ID数组[]") @RequestBody List<Long> ids) {
        operationMonthPlanService.deleteAll(ids);
        return RestResult.success(HttpStatus.OK);
    }

    @GetMapping("/info")
    @ApiOperation("查询月放款计划详情")
    @PreAuthorize("@el.check('operationMonthPlan:info')")
    public RestResult<OperationMonthPlanVo> queryOperationMonthPlanInfo(@RequestParam String id){
        return RestResult.success(operationMonthPlanService.info(id));
    }

    @PostMapping("/stop")
    @ApiOperation("启用/停用月放款计划")
    @PreAuthorize("@el.check('operationMonthPlan:enable')")
    public RestResult<Object> stopOperationMonthPlan(@Validated @RequestBody OperationMonthPlanDto resources){
        operationMonthPlanService.stopOperationMonthPlan(resources);
        return RestResult.success(HttpStatus.NO_CONTENT);
    }

    @GetMapping("/queryCapital")
    @ApiOperation("查询所有已启用资金方列表")
    @PreAuthorize("@el.check('operationMonthPlan:add')")
    public RestResult<List<Map<String, String>>> queryCapital(){
        return RestResult.success(operationMonthPlanService.queryCapital());
    }

    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('operationMonthPlan:list')")
    public void exportOperationMonthPlan(HttpServletResponse response, OperationMonthPlanDto dto) throws IOException {
    operationMonthPlanService.download(operationMonthPlanService.queryAll(dto), response);
    }
}