/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.ops.service.impl;

import com.alibaba.druid.sql.visitor.functions.Insert;
import com.jinghang.cash.api.enums.AbleStatusExt;
import com.jinghang.cash.api.enums.ActiveInactive;
import com.jinghang.cash.modules.ops.domain.OperationDayPlan;
import com.jinghang.cash.modules.ops.domain.OperationMonthPlan;
import com.jinghang.cash.modules.ops.domain.OperationProjectPlan;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghang.cash.modules.ops.domain.dto.OperationDayPlanDto;
import com.jinghang.cash.modules.ops.domain.dto.OperationProjectPlanDto;
import com.jinghang.cash.modules.ops.domain.vo.OperationMonthPlanVo;
import com.jinghang.cash.modules.ops.mapper.OperationMonthPlanMapper;
import com.jinghang.cash.modules.ops.service.OperationDayPlanService;
import com.jinghang.cash.modules.project.domain.CapitalConfig;
import com.jinghang.cash.modules.project.domain.ProjectInfo;
import com.jinghang.cash.modules.project.mapper.ProjectInfoMapper;
import com.jinghang.cash.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.cash.modules.ops.service.OperationProjectPlanService;
import com.jinghang.cash.modules.ops.mapper.OperationProjectPlanMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.ansi.AnsiOutput;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.jinghang.cash.utils.PageUtil;

import java.time.LocalDateTime;
import java.util.*;
import java.io.IOException;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.jinghang.cash.utils.PageResult;

/**
* @description 服务实现
* <AUTHOR>
* @date 2025-09-15
**/
@Service
@RequiredArgsConstructor
public class OperationProjectPlanServiceImpl extends ServiceImpl<OperationProjectPlanMapper, OperationProjectPlan> implements OperationProjectPlanService {

    private final OperationProjectPlanMapper operationProjectPlanMapper;

    private final ProjectInfoMapper projectInfoMapper;

    private final OperationDayPlanService operationDayPlanService;

    private final OperationMonthPlanMapper operationMonthPlanMapper;

    @Override
    public PageResult<OperationProjectPlan> queryAllPage(OperationProjectPlanDto criteria){
        Page<OperationProjectPlan> page = new Page<>(criteria.getPage(), criteria.getSize());
        LambdaQueryWrapper<OperationProjectPlan> wrapper = new LambdaQueryWrapper<OperationProjectPlan>();
        // TODO: 根据实际需要添加查询条件
        page = operationProjectPlanMapper.selectPage(page, wrapper);
        return PageUtil.toPage(page.getRecords(), page.getTotal());
    }

    @Override
    public List<OperationProjectPlan> queryAll(OperationProjectPlanDto criteria){
        return operationProjectPlanMapper.findAll(criteria);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(List<OperationProjectPlanDto> resources) {
        // 转换新增参数为 po对象
        List<OperationProjectPlan> result = resources.stream().map(item -> {
            OperationProjectPlan operationProjectPlan = new OperationProjectPlan();
            BeanUtils.copyProperties(item, operationProjectPlan);
            // 设置创建人，创建时间，更新人，更新时间
            operationProjectPlan.setBusiCode(operationProjectPlan.getPlanCode() + "-" + operationProjectPlan.getProjectCode());
            operationProjectPlan.setDeleted(ActiveInactive.N.getCode());
            operationProjectPlan.setCreater(String.valueOf(SecurityUtils.getCurrentUserId()));
            operationProjectPlan.setCreateTime(LocalDateTime.now());
            operationProjectPlan.setUpdater(String.valueOf(SecurityUtils.getCurrentUserId()));
            operationProjectPlan.setUpdateTime(LocalDateTime.now());
            return operationProjectPlan;
        }).collect(Collectors.toList());
        // 插入项目计划数据
        result.forEach(item -> operationProjectPlanMapper.insert(item));
        // 插入日计划数据
        // 查询所属月份
        LambdaQueryWrapper<OperationMonthPlan> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OperationMonthPlan::getPlanCode, result.get(0).getPlanCode());
        String planMonth = String.valueOf(operationMonthPlanMapper.selectOne(wrapper).getPlanMonth());
        StringBuffer stringBuffer = new StringBuffer(planMonth);
        stringBuffer.insert(4, "-");
        List<OperationDayPlanDto> operationDayPlans = new ArrayList<>();
        result.forEach(item -> {
            OperationDayPlanDto operationDayPlanDto = new OperationDayPlanDto();
            operationDayPlanDto.setBusiCode(item.getBusiCode());
            operationDayPlanDto.setPlanAmount(item.getProjectAmount());
            operationDayPlanDto.setPlanMonth(stringBuffer.toString());
            operationDayPlans.add(operationDayPlanDto);
        });
        operationDayPlanService.createPlan(operationDayPlans);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(OperationProjectPlan resources) {
        OperationProjectPlan operationProjectPlan = getById(resources.getId());
        operationProjectPlan.copy(resources);
        operationProjectPlanMapper.updateById(operationProjectPlan);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(List<Integer> ids) {
        operationProjectPlanMapper.deleteBatchIds(ids);
    }

    @Override
    public List<Map<String, String>> queryProject(String capitalCode) {
        LambdaQueryWrapper<ProjectInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjectInfo::getCapitalChannel, capitalCode);
        wrapper.eq(ProjectInfo::getEnabled, AbleStatusExt.ENABLE.name());
        List<ProjectInfo> projectInfos = projectInfoMapper.selectList(wrapper);
        List<Map<String, String>> result = projectInfos.stream().map(item -> {
            Map<String, String> vo = new HashMap<>();
            vo.put("projectCode", item.getProjectCode());
            vo.put("projectName", item.getProjectName());
            return vo;
        }).collect(Collectors.toList());
        return result;
    }

    @Override
    public void download(List<OperationProjectPlan> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (OperationProjectPlan operationProjectPlan : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("业务主键", operationProjectPlan.getBusiCode());
            map.put("放款计划配置编码", operationProjectPlan.getPlanCode());
            map.put("项目编码", operationProjectPlan.getProjectCode());
            map.put("项目名称", operationProjectPlan.getProjectName());
            map.put("项目计划金额", operationProjectPlan.getProjectAmount());
            map.put("删除标识(Y-是，N 否)", operationProjectPlan.getDeleted());
            map.put("创建时间", operationProjectPlan.getCreateTime());
            map.put("创建人", operationProjectPlan.getCreater());
            map.put("修改时间", operationProjectPlan.getUpdateTime());
            map.put("修改人", operationProjectPlan.getUpdater());
            list.add(map);
        }
        com.jinghang.cash.utils.FileUtil.downloadExcel(list, response);
    }
}